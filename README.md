# Spy Game

A Flutter game project.

## Getting Started

### Prerequisites
- Flutter SDK (>=3.4.3 <4.0.0)
- Dart SDK (>=3.4.3 <4.0.0)
- Android Studio / VS Code
- Git

### Installation

1. Clone the repository
```bash
git clone [repository-url]
```

2. Navigate to project directory
```bash
cd spy
```

3. Install dependencies
```bash
flutter pub get
```

4. Run the app
```bash
flutter run
```

### Important Notes for Development

#### Git Version Control

The following files MUST be committed:
- All source code in `lib/`
- `pubspec.yaml`
- `pubspec.lock`
- `analysis_options.yaml`
- Build configuration files
  + `android/app/build.gradle`
  + `android/build.gradle`
  + `ios/Runner.xcodeproj/project.pbxproj`
  + CMake configuration files
- Assets (images, fonts, etc.)

DO NOT commit:
- Build directories (`build/`, `.dart_tool/`, etc.)
- Local configuration files (`.flutter-plugins`, `.flutter-plugins-dependencies`)
- IDE specific files (`.idea/`, `.vscode/`)
- Generated files
- API keys and sensitive information
- Platform specific build files

#### Dependencies Management
- Always commit both `pubspec.yaml` and `pubspec.lock`
- `pubspec.lock` ensures all developers use the same package versions
- After pulling new changes:
```bash
flutter pub get
```

If you encounter any issues:
```bash
flutter clean
flutter pub get
```

#### Environment Setup
1. Android Setup:
   - Add Google Mobile Ads configuration in `AndroidManifest.xml`
   - Configure minimum SDK version in `android/app/build.gradle`

2. iOS Setup:
   - Configure required permissions in `Info.plist`
   - Set up pod dependencies

### Project Structure
```
lib/
├── data/           # Data layer
├── domain/         # Business logic
├── presentation/   # UI components
└── main.dart       # Entry point
```

### Features
- Feature 1
- Feature 2
- Feature 3

### Dependencies
- flutter_bloc: ^8.1.4
- equatable: ^2.0.5
- google_fonts: ^6.2.1
- audioplayers: ^5.2.0
- hive: ^2.2.3
- google_mobile_ads: ^3.1.0

## Resources

For help getting started with Flutter development:
- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)
- [Online documentation](https://docs.flutter.dev/)

## License

[License Type] - see the [LICENSE.md](LICENSE.md) file for details
