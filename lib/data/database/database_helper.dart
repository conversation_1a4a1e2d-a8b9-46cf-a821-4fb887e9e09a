import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:flutter/services.dart';
import 'dart:io';

class DatabaseHelper {
  static final DatabaseHelper instance = DatabaseHelper._init();
  static Database? _database;

  DatabaseHelper._init();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB('spy.db');
    return _database!;
  }

  Future<Database> _initDB(String filePath) async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, filePath);

    // Check if database exists
    if (!await File(path).exists()) {
      // Copy database from assets
      await _copyDatabaseFromAssets(path);
    }

    return await openDatabase(
      path,
      version: 5, // Increased version to trigger upgrade
      onCreate: _createDB,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _copyDatabaseFromAssets(String path) async {
    try {
      // Load database from assets
      final ByteData data = await rootBundle.load('assets/spy.db');
      final List<int> bytes = data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);

      // Write to file
      await File(path).writeAsBytes(bytes);
    } catch (e) {
      await openDatabase(
        path,
        version: 5,
        onCreate: _createDB,
      );
    }
  }

  Future<void> initializeDatabase() async {
    try {
      _database ??= await _initDB('spy.db');
    } catch (e) {
    }
  }

  Future _createDB(Database db, int version) async {
    await db.execute('''
      CREATE TABLE character (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        image TEXT NOT NULL,
        description TEXT NOT NULL,
        isUnlocked INTEGER NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE story_line (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        description TEXT,
        isUnlocked INTEGER NOT NULL DEFAULT 0
      )
    ''');

    await db.execute('''
      CREATE TABLE card (
        id TEXT PRIMARY KEY,
        characterId INTEGER,
        description TEXT,
        type TEXT NOT NULL,
        leftEffect TEXT NOT NULL,
        rightEffect TEXT NOT NULL,
        leftText TEXT NOT NULL,
        rightText TEXT NOT NULL,
        parent_card_id TEXT NULL,
        unlockId INTEGER,
        isDisable INTEGER DEFAULT 0,
        story_unlock_id INTEGER,
        FOREIGN KEY (characterId) REFERENCES character (id),
        FOREIGN KEY (parent_card_id) REFERENCES card (id),
        FOREIGN KEY (story_unlock_id) REFERENCES story_line (id),
        FOREIGN KEY (unlockId) REFERENCES character (id)
      )
    ''');
  }

  Future _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      try {
        await db.execute('ALTER TABLE card ADD COLUMN parent_card_id TEXT NULL');
      } catch (e) {
      }

      try {
        await db.execute('ALTER TABLE character RENAME COLUMN imgUrl TO image');
      } catch (e) {
      }
    }

    if (oldVersion < 3) {
      try {
        await db.execute('ALTER TABLE card ADD COLUMN unlockId TEXT NULL');
      } catch (e) {
      }

      try {
        await db.execute('ALTER TABLE card ADD COLUMN isDisable INTEGER DEFAULT 0');
      } catch (e) {
      }
    }

    if (oldVersion < 4) {
      try {
        await db.execute('ALTER TABLE card ADD COLUMN unlockId TEXT NULL');
      } catch (e) {}

      try {
        await db.execute('ALTER TABLE card ADD COLUMN isDisable INTEGER DEFAULT 0');
      } catch (e) {}
    }

    if (oldVersion < 5) {
      try {
        await db.execute('''
          CREATE TABLE IF NOT EXISTS story_line (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT,
            description TEXT,
            isUnlocked INTEGER NOT NULL DEFAULT 0
          )
        ''');
      } catch (e) {}

      try {
        await db.execute('ALTER TABLE card ADD COLUMN story_unlock_id INTEGER');
      } catch (e) {}
    }
  }

}
