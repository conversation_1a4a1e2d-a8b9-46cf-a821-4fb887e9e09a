import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseHelper {
  static final DatabaseHelper instance = DatabaseHelper._init();
  static Database? _database;

  DatabaseHelper._init();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB('spy.db');
    return _database!;
  }

  Future<Database> _initDB(String filePath) async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, filePath);
    return await openDatabase(
      path,
      version: 4, // Increased version to trigger upgrade
      onCreate: _createDB,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> initializeDatabase() async {
    try {
      _database ??= await _initDB('spy.db');
    } catch (e) {
    }
  }

  Future _createDB(Database db, int version) async {
    await db.execute('''
      CREATE TABLE character (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        image TEXT NOT NULL,
        description TEXT NOT NULL,
        isUnlocked INTEGER NOT NULL
      )
    ''');
    await db.execute('''
      CREATE TABLE card (
        id TEXT PRIMARY KEY,
        characterId INTEGER,
        description TEXT,
        type TEXT NOT NULL,
        leftEffect TEXT NOT NULL,
        rightEffect TEXT NOT NULL,
        leftText TEXT NOT NULL,
        rightText TEXT NOT NULL,
        parent_card_id TEXT NULL,
        unlockId TEXT NULL,
        isDisable INTEGER DEFAULT 0,
        FOREIGN KEY (characterId) REFERENCES character (id),
        FOREIGN KEY (parent_card_id) REFERENCES card (id)
      )
    ''');
  }

  Future _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      try {
        await db.execute('ALTER TABLE card ADD COLUMN parent_card_id TEXT NULL');
      } catch (e) {
      }

      try {
        await db.execute('ALTER TABLE character RENAME COLUMN imgUrl TO image');
      } catch (e) {
      }
    }

    if (oldVersion < 3) {
      try {
        await db.execute('ALTER TABLE card ADD COLUMN unlockId TEXT NULL');
      } catch (e) {
      }

      try {
        await db.execute('ALTER TABLE card ADD COLUMN isDisable INTEGER DEFAULT 0');
      } catch (e) {
      }
    }

    if (oldVersion < 4) {
      try {
        await db.execute('ALTER TABLE card ADD COLUMN unlockId TEXT NULL');
      } catch (e) {}

      try {
        await db.execute('ALTER TABLE card ADD COLUMN isDisable INTEGER DEFAULT 0');
      } catch (e) {}
    }
  }

  Future<void> insertMockData() async {
    final db = await database;

    // Check if data already exists
    final List<Map<String, dynamic>> existingCharacters = await db.query('character');
    if (existingCharacters.isNotEmpty) {
      return;
    }

    // Insert 20 characters (keeping your existing characters and adding 10 more)
    final List<Map<String, dynamic>> characters = [
      {'id': 1, 'name': 'Phóng viên Mỹ', 'image': 'assets/images/lyly.webp', 'description': 'Nhà báo người Mỹ', 'isUnlocked': 1},
      {'id': 2, 'name': 'Edward Lansdale', 'image': 'assets/images/edward.webp', 'description': 'Đại tá tình báo Mỹ', 'isUnlocked': 0},
      {'id': 3, 'name': 'Nguyễn', 'image': 'assets/images/nguyen.webp', 'description': 'Nhân vật bí ẩn', 'isUnlocked': 0},
      {'id': 4, 'name': 'Lucien Conein', 'image': 'assets/images/conein.webp', 'description': 'Điệp viên CIA', 'isUnlocked': 0},
      {'id': 5, 'name': 'Robert Shaplen', 'image': 'assets/images/shaplen.webp', 'description': 'Phóng viên The New Yorker', 'isUnlocked': 0},
      {'id': 6, 'name': 'Laura', 'image': 'assets/images/laura.webp', 'description': 'Thư ký tòa báo', 'isUnlocked': 0},
      {'id': 7, 'name': 'James', 'image': 'assets/images/james.webp', 'description': 'Biên tập viên', 'isUnlocked': 0},
      {'id': 8, 'name': 'Henry', 'image': 'assets/images/henry.webp', 'description': 'Phóng viên kỳ cựu', 'isUnlocked': 0},
      {'id': 9, 'name': 'Wilson', 'image': 'assets/images/wilson.webp', 'description': 'Trưởng phòng tin tức', 'isUnlocked': 0},
      {'id': 10, 'name': 'Jayson', 'image': 'assets/images/jayson.webp', 'description': 'Nhiếp ảnh gia', 'isUnlocked': 0},
      {'id': 11, 'name': 'Colonel Thompson', 'image': 'assets/images/thompson.webp', 'description': 'Sĩ quan quân đội', 'isUnlocked': 0},
      {'id': 12, 'name': 'Dr. Sarah Chen', 'image': 'assets/images/chen.webp', 'description': 'Bác sĩ tâm lý', 'isUnlocked': 0},
      {'id': 13, 'name': 'Agent Martinez', 'image': 'assets/images/martinez.webp', 'description': 'Đặc vụ FBI', 'isUnlocked': 0},
      {'id': 14, 'name': 'Ambassador Davis', 'image': 'assets/images/davis.webp', 'description': 'Đại sứ Mỹ', 'isUnlocked': 0},
      {'id': 15, 'name': 'General Harrison', 'image': 'assets/images/harrison.webp', 'description': 'Tướng tác chiến', 'isUnlocked': 0},
      {'id': 16, 'name': 'Professor Kim', 'image': 'assets/images/kim.webp', 'description': 'Giáo sư lịch sử', 'isUnlocked': 0},
      {'id': 17, 'name': 'Captain Rodriguez', 'image': 'assets/images/rodriguez.webp', 'description': 'Đại úy hải quân', 'isUnlocked': 0},
      {'id': 18, 'name': 'Ms. Johnson', 'image': 'assets/images/johnson.webp', 'description': 'Thư ký ngoại giao', 'isUnlocked': 0},
      {'id': 19, 'name': 'Agent Black', 'image': 'assets/images/black.webp', 'description': 'Điệp viên bí mật', 'isUnlocked': 0},
      {'id': 20, 'name': 'Director White', 'image': 'assets/images/white.webp', 'description': 'Giám đốc CIA', 'isUnlocked': 0},
    ];

    for (var character in characters) {
      await db.insert('character', character);
    }

    // Insert 60 cards with 8 decision trees using Y/N notation
    final List<Map<String, dynamic>> cards = [
      // Main storyline cards (1-12)
      {
        'id': '1',
        'characterId': 1,
        'description': 'Chào mừng bạn đến với tòa soạn The Times',
        'type': 'intro',
        'leftEffect': '{"internalTrust": 1, "externalTrust": 0, "mentalStability": 1, "socialLinks": 0}',
        'rightEffect': '{"internalTrust": 0, "externalTrust": 1, "mentalStability": -1, "socialLinks": 1}',
        'leftText': 'Cảm ơn, tôi rất vui được làm việc ở đây',
        'rightText': 'Cảm ơn, tôi sẽ cố gắng hết sức',
        'parent_card_id': null
      },
      {
        'id': '2',
        'characterId': 2,
        'description': 'Tình hình Việt Nam đang rất căng thẳng',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 1, "externalTrust": -1, "mentalStability": 0, "socialLinks": 1}',
        'rightEffect': '{"internalTrust": -1, "externalTrust": 1, "mentalStability": 0, "socialLinks": 0}',
        'leftText': 'Chúng ta nên đưa tin thật khách quan',
        'rightText': 'Đây là cơ hội để làm một điều gì đó lớn lao',
        'parent_card_id': null
      },
      {
        'id': '3',
        'characterId': 3,
        'description': 'Bạn có muốn tham gia một nhiệm vụ đặc biệt không?',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 0, "externalTrust": 1, "mentalStability": 1, "socialLinks": 1}',
        'rightEffect': '{"internalTrust": 1, "externalTrust": -1, "mentalStability": 1, "socialLinks": 0}',
        'leftText': 'Tôi cần biết thêm chi tiết',
        'rightText': 'Không, tôi chỉ muốn làm một phóng viên bình thường',
        'parent_card_id': null
      },
      {
        'id': '4',
        'characterId': 4,
        'description': 'Chúng tôi cần thông tin từ phía bên kia',
        'type': 'normal',
        'leftEffect': '{"internalTrust": -1, "externalTrust": 2, "mentalStability": 0, "socialLinks": 1}',
        'rightEffect': '{"internalTrust": 1, "externalTrust": -1, "mentalStability": 1, "socialLinks": 0}',
        'leftText': 'Tôi sẽ cố gắng tìm hiểu',
        'rightText': 'Điều đó quá nguy hiểm',
        'parent_card_id': null
      },
      {
        'id': '5',
        'characterId': 5,
        'description': 'Có một cuộc họp báo quan trọng',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 0, "externalTrust": 1, "mentalStability": 1, "socialLinks": -1}',
        'rightEffect': '{"internalTrust": 1, "externalTrust": 0, "mentalStability": -1, "socialLinks": 1}',
        'leftText': 'Tôi sẽ đến dự',
        'rightText': 'Tôi có việc khác quan trọng hơn',
        'parent_card_id': null
      },
      {
        'id': '6',
        'characterId': 6,
        'description': 'Có một bức điện mật gửi đến',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 1, "externalTrust": 0, "mentalStability": 0, "socialLinks": 1}',
        'rightEffect': '{"internalTrust": 0, "externalTrust": 1, "mentalStability": -1, "socialLinks": 1}',
        'leftText': 'Đọc ngay lập tức',
        'rightText': 'Để đó đọc sau',
        'parent_card_id': null
      },
      {
        'id': '7',
        'characterId': 7,
        'description': 'Bài viết của bạn cần chỉnh sửa',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 1, "externalTrust": -1, "mentalStability": 1, "socialLinks": 0}',
        'rightEffect': '{"internalTrust": -1, "externalTrust": 1, "mentalStability": 0, "socialLinks": 1}',
        'leftText': 'Đồng ý sửa theo yêu cầu',
        'rightText': 'Giữ nguyên quan điểm',
        'parent_card_id': null
      },
      {
        'id': '8',
        'characterId': 8,
        'description': 'Có người muốn gặp bạn ở quán café',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 0, "externalTrust": 1, "mentalStability": -1, "socialLinks": 2}',
        'rightEffect': '{"internalTrust": 1, "externalTrust": -1, "mentalStability": 1, "socialLinks": -1}',
        'leftText': 'Đồng ý gặp',
        'rightText': 'Từ chối khéo',
        'parent_card_id': null
      },
      {
        'id': '9',
        'characterId': 9,
        'description': 'Một nguồn tin quan trọng xuất hiện',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 1, "externalTrust": 1, "mentalStability": -1, "socialLinks": 1}',
        'rightEffect': '{"internalTrust": -1, "externalTrust": 0, "mentalStability": 1, "socialLinks": 0}',
        'leftText': 'Kiểm tra độ tin cậy',
        'rightText': 'Đưa tin ngay lập tức',
        'parent_card_id': null
      },
      {
        'id': '10',
        'characterId': 10,
        'description': 'Bạn nhận được một lời mời tham gia dự án mới',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 2, "externalTrust": 0, "mentalStability": -1, "socialLinks": 1}',
        'rightEffect': '{"internalTrust": -1, "externalTrust": 1, "mentalStability": 1, "socialLinks": 0}',
        'leftText': 'Tham gia dự án',
        'rightText': 'Từ chối',
        'parent_card_id': null
      },
      {
        'id': '11',
        'characterId': 11,
        'description': 'Bạn phát hiện một lỗi nghiêm trọng trong hệ thống',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 1, "externalTrust": -1, "mentalStability": 0, "socialLinks": 0}',
        'rightEffect': '{"internalTrust": -1, "externalTrust": 1, "mentalStability": 1, "socialLinks": -1}',
        'leftText': 'Báo cáo lỗi',
        'rightText': 'Bỏ qua',
        'parent_card_id': null
      },
      {
        'id': '12',
        'characterId': 12,
        'description': 'Bạn nhận được một email nặc danh',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 0, "externalTrust": 1, "mentalStability": -1, "socialLinks": 0}',
        'rightEffect': '{"internalTrust": 1, "externalTrust": -1, "mentalStability": 1, "socialLinks": 1}',
        'leftText': 'Trả lời email',
        'rightText': 'Xóa email',
        'parent_card_id': null
      },

      // Decision Tree 1: Card 4 branches (4L, 4R)
      {
        'id': '4L',
        'characterId': 4,
        'description': 'Bạn đồng ý giúp đỡ. Đây là thông tin bí mật đầu tiên...',
        'type': 'normal',
        'leftEffect': '{"internalTrust": -1, "externalTrust": 3, "mentalStability": -1, "socialLinks": 0}',
        'rightEffect': '{"internalTrust": 0, "externalTrust": 1, "mentalStability": 1, "socialLinks": -1}',
        'leftText': 'Thực hiện nhiệm vụ ngay lập tức',
        'rightText': 'Cần thời gian chuẩn bị kỹ hơn',
        'parent_card_id': '4'
      },
      {
        'id': '4R',
        'characterId': 4,
        'description': 'Bạn từ chối. Conein nhìn bạn với ánh mắt thất vọng...',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 2, "externalTrust": -1, "mentalStability": 1, "socialLinks": 1}',
        'rightEffect': '{"internalTrust": 1, "externalTrust": -1, "mentalStability": 0, "socialLinks": 0}',
        'leftText': 'Giải thích lý do từ chối',
        'rightText': 'Im lặng rời đi',
        'parent_card_id': '4'
      },

      // Decision Tree 2: Card 4L branches (4LL, 4LR)
      {
        'id': '4LL',
        'characterId': 4,
        'description': 'Bạn hành động ngay lập tức và thu thập được thông tin quan trọng',
        'type': 'normal',
        'leftEffect': '{"internalTrust": -1, "externalTrust": 2, "mentalStability": -1, "socialLinks": 1}',
        'rightEffect': '{"internalTrust": 1, "externalTrust": 1, "mentalStability": 0, "socialLinks": 0}',
        'leftText': 'Báo cáo thông tin ngay',
        'rightText': 'Giữ bí mật một thời gian',
        'parent_card_id': '4L'
      },
      {
        'id': '4LR',
        'characterId': 4,
        'description': 'Bạn quyết định chuẩn bị kỹ càng hơn. Điều này tốn thời gian nhưng an toàn hơn',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 1, "externalTrust": 1, "mentalStability": 1, "socialLinks": 0}',
        'rightEffect': '{"internalTrust": 0, "externalTrust": 2, "mentalStability": -1, "socialLinks": 1}',
        'leftText': 'Tiếp tục kế hoạch an toàn',
        'rightText': 'Thay đổi chiến lược',
        'parent_card_id': '4L'
      },

      // Decision Tree 3: Card 4R branches (4RL, 4RR)
      {
        'id': '4RL',
        'characterId': 4,
        'description': 'Bạn giải thích rằng mình muốn tập trung vào việc báo chí',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 1, "externalTrust": 0, "mentalStability": 1, "socialLinks": 1}',
        'rightEffect': '{"internalTrust": 0, "externalTrust": -1, "mentalStability": 0, "socialLinks": 2}',
        'leftText': 'Tiếp tục làm việc bình thường',
        'rightText': 'Tìm hiểu thêm về tổ chức',
        'parent_card_id': '4R'
      },
      {
        'id': '4RR',
        'characterId': 4,
        'description': 'Bạn im lặng rời đi. Conein ghi nhận thái độ này',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 2, "externalTrust": -1, "mentalStability": 0, "socialLinks": -1}',
        'rightEffect': '{"internalTrust": 1, "externalTrust": -1, "mentalStability": 1, "socialLinks": 0}',
        'leftText': 'Tránh né các cuộc gặp sau này',
        'rightText': 'Hành động như không có gì xảy ra',
        'parent_card_id': '4R'
      },

      // Decision Tree 4: Card 8 branches (8L, 8R)
      {
        'id': '8L',
        'characterId': 8,
        'description': 'Bạn đến quán café và gặp một người bí ẩn',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 0, "externalTrust": 2, "mentalStability": -1, "socialLinks": 2}',
        'rightEffect': '{"internalTrust": 1, "externalTrust": 0, "mentalStability": 0, "socialLinks": 1}',
        'leftText': 'Lắng nghe câu chuyện của họ',
        'rightText': 'Hỏi về danh tính',
        'parent_card_id': '8'
      },
      {
        'id': '8R',
        'characterId': 8,
        'description': 'Bạn từ chối gặp mặt một cách lịch sự',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 1, "externalTrust": -1, "mentalStability": 1, "socialLinks": -1}',
        'rightEffect': '{"internalTrust": 0, "externalTrust": 0, "mentalStability": 2, "socialLinks": 0}',
        'leftText': 'Hẹn gặp vào lúc khác',
        'rightText': 'Từ chối hoàn toàn',
        'parent_card_id': '8'
      },

      // Decision Tree 5: Card 8L branches (8LL, 8LR)
      {
        'id': '8LL',
        'characterId': 13,
        'description': 'Người bí ẩn kể về một âm mưu lớn đang diễn ra',
        'type': 'normal',
        'leftEffect': '{"internalTrust": -1, "externalTrust": 3, "mentalStability": -1, "socialLinks": 2}',
        'rightEffect': '{"internalTrust": 1, "externalTrust": 1, "mentalStability": 1, "socialLinks": 1}',
        'leftText': 'Tin tưởng hoàn toàn',
        'rightText': 'Hoài nghi thông tin',
        'parent_card_id': '8L'
      },
      {
        'id': '8LR',
        'characterId': 14,
        'description': 'Bạn hỏi về danh tính và nhận được câu trả lời mơ hồ',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 1, "externalTrust": 0, "mentalStability": 0, "socialLinks": 1}',
        'rightEffect': '{"internalTrust": 0, "externalTrust": 1, "mentalStability": -1, "socialLinks": 2}',
        'leftText': 'Kết thúc cuộc gặp',
        'rightText': 'Tiếp tục tìm hiểu',
        'parent_card_id': '8L'
      },

      // Decision Tree 6: Card 10 branches (10L, 10R)
      {
        'id': '10L',
        'characterId': 15,
        'description': 'Bạn tham gia dự án và phát hiện nó liên quan đến tình báo',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 1, "externalTrust": 2, "mentalStability": -1, "socialLinks": 2}',
        'rightEffect': '{"internalTrust": 2, "externalTrust": 0, "mentalStability": 0, "socialLinks": 0}',
        'leftText': 'Tiếp tục tham gia',
        'rightText': 'Rút lui ngay lập tức',
        'parent_card_id': '10'
      },
      {
        'id': '10R',
        'characterId': 16,
        'description': 'Bạn từ chối dự án và tập trung vào công việc hiện tại',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 2, "externalTrust": -1, "mentalStability": 1, "socialLinks": 0}',
        'rightEffect': '{"internalTrust": 1, "externalTrust": 0, "mentalStability": 0, "socialLinks": 1}',
        'leftText': 'Tập trung làm việc',
        'rightText': 'Tìm hiểu thêm về dự án',
        'parent_card_id': '10'
      },

      // Decision Tree 7: Card 10L branches (10LL, 10LR)
      {
        'id': '10LL',
        'characterId': 17,
        'description': 'Bạn tiếp tục và trở thành một phần của mạng lưới tình báo',
        'type': 'normal',
        'leftEffect': '{"internalTrust": -1, "externalTrust": 4, "mentalStability": 1, "socialLinks": 3}',
        'rightEffect': '{"internalTrust": 0, "externalTrust": 2, "mentalStability": 1, "socialLinks": 1}',
        'leftText': 'Chấp nhận vai trò mới',
        'rightText': 'Giữ khoảng cách',
        'parent_card_id': '10L'
      },
      {
        'id': '10LR',
        'characterId': 18,
        'description': 'Bạn rút lui và cố gắng quên đi những gì đã biết',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 3, "externalTrust": -1, "mentalStability": 2, "socialLinks": -1}',
        'rightEffect': '{"internalTrust": 2, "externalTrust": -1, "mentalStability": 1, "socialLinks": 0}',
        'leftText': 'Quên đi mọi thứ',
        'rightText': 'Giữ bí mật nhưng theo dõi',
        'parent_card_id': '10L'
      },

      // Decision Tree 8: Card 12 branches (12L, 12R)
      {
        'id': '12L',
        'characterId': 19,
        'description': 'Bạn trả lời email và nhận được chỉ dẫn bí mật',
        'type': 'normal',
        'leftEffect': '{"internalTrust": -1, "externalTrust": 2, "mentalStability": -1, "socialLinks": 1}',
        'rightEffect': '{"internalTrust": 1, "externalTrust": 1, "mentalStability": 0, "socialLinks": 0}',
        'leftText': 'Làm theo chỉ dẫn',
        'rightText': 'Báo cáo cho cấp trên',
        'parent_card_id': '12'
      },
      {
        'id': '12R',
        'characterId': 20,
        'description': 'Bạn xóa email nhưng có người đã biết bạn đã đọc nó',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 1, "externalTrust": -1, "mentalStability": -1, "socialLinks": 0}',
        'rightEffect': '{"internalTrust": 2, "externalTrust": -1, "mentalStability": 1, "socialLinks": -1}',
        'leftText': 'Chuẩn bị cho hậu quả',
        'rightText': 'Hành động như không có gì',
        'parent_card_id': '12'
      },

      // Additional leaf cards to reach 60 total
      {
        'id': '12LL',
        'characterId': 1,
        'description': 'Bạn làm theo chỉ dẫn và phát hiện ra sự thật kinh hoàng',
        'type': 'normal',
        'leftEffect': '{"internalTrust": -1, "externalTrust": 3, "mentalStability": 1, "socialLinks": 2}',
        'rightEffect': '{"internalTrust": 0, "externalTrust": 1, "mentalStability": -1, "socialLinks": 0}',
        'leftText': 'Công bố sự thật',
        'rightText': 'Giữ bí mật',
        'parent_card_id': '12L'
      },
      {
        'id': '12LR',
        'characterId': 2,
        'description': 'Bạn báo cáo và được đưa vào chương trình bảo vệ',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 2, "externalTrust": 0, "mentalStability": 1, "socialLinks": -1}',
        'rightEffect': '{"internalTrust": 1, "externalTrust": 1, "mentalStability": 0, "socialLinks": -1}',
        'leftText': 'Chấp nhận bảo vệ',
        'rightText': 'Từ chối và tự bảo vệ mình',
        'parent_card_id': '12L'
      },
      {
        'id': '12RL',
        'characterId': 3,
        'description': 'Bạn chuẩn bị đối phó nhưng không có gì xảy ra',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 1, "externalTrust": 0, "mentalStability": 0, "socialLinks": 0}',
        'rightEffect': '{"internalTrust": 0, "externalTrust": 0, "mentalStability": 1, "socialLinks": 1}',
        'leftText': 'Tiếp tục cảnh giác',
        'rightText': 'Thả lỏng và quay lại bình thường',
        'parent_card_id': '12R'
      },
      {
        'id': '12RR',
        'characterId': 4,
        'description': 'Bạn hành động bình thường nhưng bị theo dõi',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 0, "externalTrust": -1, "mentalStability": -1, "socialLinks": -1}',
        'rightEffect': '{"internalTrust": 1, "externalTrust": -1, "mentalStability": 0, "socialLinks": 0}',
        'leftText': 'Phát hiện ra sự theo dõi',
        'rightText': 'Không nhận ra gì',
        'parent_card_id': '12R'
      },

      // Final cards to complete 60
      {
        'id': '4LLL',
        'characterId': 5,
        'description': 'Bạn báo cáo ngay và được khen thưởng',
        'type': 'normal',
        'leftEffect': '{"internalTrust": -1, "externalTrust": 3, "mentalStability": -1, "socialLinks": 1}',
        'rightEffect': '{"internalTrust": 0, "externalTrust": 2, "mentalStability": 0, "socialLinks": 0}',
        'leftText': 'Tiếp tục nhiệm vụ',
        'rightText': 'Yêu cầu nghỉ ngơi',
        'parent_card_id': '4LL'
      },
      {
        'id': '4LLR',
        'characterId': 6,
        'description': 'Bạn giữ bí mật và cảm thấy gánh nặng',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 1, "externalTrust": 1, "mentalStability": 2, "socialLinks": 0}',
        'rightEffect': '{"internalTrust": 2, "externalTrust": 0, "mentalStability": -1, "socialLinks": 1}',
        'leftText': 'Tìm người tâm sự',
        'rightText': 'Chịu đựng một mình',
        'parent_card_id': '4LL'
      },
      {
        'id': '8LLL',
        'characterId': 7,
        'description': 'Bạn tin tưởng hoàn toàn và bị lôi kéo vào vòng xoáy',
        'type': 'normal',
        'leftEffect': '{"internalTrust": -1, "externalTrust": 4, "mentalStability": -1, "socialLinks": 3}',
        'rightEffect': '{"internalTrust": 0, "externalTrust": 2, "mentalStability": 1, "socialLinks": 1}',
        'leftText': 'Cam kết hoàn toàn',
        'rightText': 'Giữ một phần nghi ngờ',
        'parent_card_id': '8LL'
      },
      {
        'id': '8LLR',
        'characterId': 8,
        'description': 'Bạn hoài nghi và quyết định tự điều tra',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 2, "externalTrust": 0, "mentalStability": 1, "socialLinks": 0}',
        'rightEffect': '{"internalTrust": 1, "externalTrust": 1, "mentalStability": 0, "socialLinks": -1}',
        'leftText': 'Điều tra một mình',
        'rightText': 'Tìm đồng minh',
        'parent_card_id': '8LL'
      },
      {
        'id': '10LLL',
        'characterId': 9,
        'description': 'Bạn chấp nhận vai trò và trở thành điệp viên chuyên nghiệp',
        'type': 'normal',
        'leftEffect': '{"internalTrust": -1, "externalTrust": 5, "mentalStability": 1, "socialLinks": 4}',
        'rightEffect': '{"internalTrust": -1, "externalTrust": 3, "mentalStability": 1, "socialLinks": 2}',
        'leftText': 'Trở thành điệp viên hàng đầu',
        'rightText': 'Giữ vai trò thụ động',
        'parent_card_id': '10LL'
      },
      {
        'id': '10LLR',
        'characterId': 10,
        'description': 'Bạn giữ khoảng cách nhưng vẫn bị cuốn vào',
        'type': 'normal',
        'leftEffect': '{"internalTrust": 0, "externalTrust": 2, "mentalStability": 1, "socialLinks": 1}',
        'rightEffect': '{"internalTrust": 1, "externalTrust": 1, "mentalStability": 0, "socialLinks": 0}',
        'leftText': 'Cố gắng thoát ra',
        'rightText': 'Chấp nhận tình cảnh',
        'parent_card_id': '10YY'
      },

      // Item cards for death prevention
      {
        'id': 'item_eye',
        'characterId': null,
        'description': 'Bạn sử dụng vật phẩm Mắt để thoát khỏi tình huống nguy hiểm. Lòng tin nội bộ được khôi phục.',
        'type': 'item',
        'leftEffect': '{"internalTrust": 5, "externalTrust": 0, "mentalStability": 0, "socialLinks": 0}',
        'rightEffect': '{"internalTrust": 5, "externalTrust": 0, "mentalStability": 0, "socialLinks": 0}',
        'leftText': 'Tiếp tục',
        'rightText': 'Tiếp tục',
        'parent_card_id': null
      },
      {
        'id': 'item_badge',
        'characterId': null,
        'description': 'Bạn sử dụng vật phẩm Huy hiệu để thoát khỏi tình huống nguy hiểm. Lòng tin bên ngoài được khôi phục.',
        'type': 'item',
        'leftEffect': '{"internalTrust": 0, "externalTrust": 5, "mentalStability": 0, "socialLinks": 0}',
        'rightEffect': '{"internalTrust": 0, "externalTrust": 5, "mentalStability": 0, "socialLinks": 0}',
        'leftText': 'Tiếp tục',
        'rightText': 'Tiếp tục',
        'parent_card_id': null
      },
      {
        'id': 'item_heart',
        'characterId': null,
        'description': 'Bạn sử dụng vật phẩm Trái tim để thoát khỏi tình huống nguy hiểm. Sức khỏe tinh thần được khôi phục.',
        'type': 'item',
        'leftEffect': '{"internalTrust": 0, "externalTrust": 0, "mentalStability": 5, "socialLinks": 0}',
        'rightEffect': '{"internalTrust": 0, "externalTrust": 0, "mentalStability": 5, "socialLinks": 0}',
        'leftText': 'Tiếp tục',
        'rightText': 'Tiếp tục',
        'parent_card_id': null
      },
      {
        'id': 'item_newspaper',
        'characterId': null,
        'description': 'Bạn sử dụng vật phẩm Báo chí để thoát khỏi tình huống nguy hiểm. Mối quan hệ xã hội được khôi phục.',
        'type': 'item',
        'leftEffect': '{"internalTrust": 0, "externalTrust": 0, "mentalStability": 0, "socialLinks": 5}',
        'rightEffect': '{"internalTrust": 0, "externalTrust": 0, "mentalStability": 0, "socialLinks": 5}',
        'leftText': 'Tiếp tục',
        'rightText': 'Tiếp tục',
        'parent_card_id': null
      },

      // Death cards
      {
        'id': 'death_internal',
        'characterId': null,
        'description': 'Lòng tin nội bộ của bạn đã cạn kiệt. Bạn không còn tin tưởng vào bản thân.',
        'type': 'internalDeath',
        'leftEffect': '{"internalTrust": 0, "externalTrust": 0, "mentalStability": 0, "socialLinks": 0}',
        'rightEffect': '{"internalTrust": 0, "externalTrust": 0, "mentalStability": 0, "socialLinks": 0}',
        'leftText': 'Kết thúc',
        'rightText': 'Kết thúc',
        'parent_card_id': null
      },
      {
        'id': 'death_external',
        'characterId': null,
        'description': 'Lòng tin bên ngoài của bạn đã mất hết. Không ai còn tin tưởng bạn.',
        'type': 'externalDeath',
        'leftEffect': '{"internalTrust": 0, "externalTrust": 0, "mentalStability": 0, "socialLinks": 0}',
        'rightEffect': '{"internalTrust": 0, "externalTrust": 0, "mentalStability": 0, "socialLinks": 0}',
        'leftText': 'Kết thúc',
        'rightText': 'Kết thúc',
        'parent_card_id': null
      },
      {
        'id': 'death_mental',
        'characterId': null,
        'description': 'Sức khỏe tinh thần của bạn đã suy sụp hoàn toàn.',
        'type': 'mentalDeath',
        'leftEffect': '{"internalTrust": 0, "externalTrust": 0, "mentalStability": 0, "socialLinks": 0}',
        'rightEffect': '{"internalTrust": 0, "externalTrust": 0, "mentalStability": 0, "socialLinks": 0}',
        'leftText': 'Kết thúc',
        'rightText': 'Kết thúc',
        'parent_card_id': null
      },
      {
        'id': 'death_social',
        'characterId': null,
        'description': 'Mối quan hệ xã hội của bạn đã tan vỡ hoàn toàn.',
        'type': 'socialDeath',
        'leftEffect': '{"internalTrust": 0, "externalTrust": 0, "mentalStability": 0, "socialLinks": 0}',
        'rightEffect': '{"internalTrust": 0, "externalTrust": 0, "mentalStability": 0, "socialLinks": 0}',
        'leftText': 'Kết thúc',
        'rightText': 'Kết thúc',
        'parent_card_id': null
      },
    ];

    for (var card in cards) {
      await db.insert('card', card);
    }
  }
}
