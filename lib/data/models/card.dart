import 'character.dart';

class Card{
  final String id;
  final int? characterId;
  final Character? character;
  final String description;
  final String type;
  final String leftEffect;
  final String rightEffect;
  final String leftText;
  final String rightText;
  final String? parentCardId;
  final String? unlockId;
  final int isDisable;

  Card({
    required this.id,
    required this.characterId,
    this.character, // Thêm parameter character
    required this.description,
    required this.type,
    required this.leftEffect,
    required this.rightEffect,
    required this.leftText,
    required this.rightText,
    this.parentCardId,
    this.unlockId,
    this.isDisable = 0,
  });

  // Parse effect string into map (hỗ trợ cả format cũ và mới)
  Map<String, int> parseEffect(String effectString) {
    // Nếu là JSON format mới
    if (effectString.startsWith('{')) {
      try {
        final Map<String, dynamic> jsonMap = {};
        final cleanString = effectString.replaceAll('{', '').replaceAll('}', '').replaceAll('"', '');
        final pairs = cleanString.split(',');

        for (String pair in pairs) {
          final keyValue = pair.split(':');
          if (keyValue.length == 2) {
            final key = keyValue[0].trim();
            final value = int.parse(keyValue[1].trim());
            jsonMap[key] = value;
          }
        }

        return {
          'internalTrust': jsonMap['internalTrust'] ?? 0,
          'externalTrust': jsonMap['externalTrust'] ?? 0,
          'mentalStability': jsonMap['mentalStability'] ?? 0,
          'socialLinks': jsonMap['socialLinks'] ?? 0,
        };
      } catch (e) {
        return {
          'internalTrust': 0,
          'externalTrust': 0,
          'mentalStability': 0,
          'socialLinks': 0,
        };
      }
    } else {
      // Format cũ với dấu phẩy
      final values = effectString.split(',').map((e) => int.parse(e.trim())).toList();
      if (values.length != 4) {
        throw ArgumentError('Effect string must contain exactly 4 values');
      }

      return {
        'internalTrust': values[0],
        'externalTrust': values[1],
        'mentalStability': values[2],
        'socialLinks': values[3],
      };
    }
  }

  // Get parsed left effect
  Map<String, int> get leftEffectMap => parseEffect(leftEffect);

  // Get parsed right effect
  Map<String, int> get rightEffectMap => parseEffect(rightEffect);

  // Kiểm tra xem thẻ này có phải là thẻ con không
  bool get isChildCard => parentCardId != null;

  // Kiểm tra xem thẻ này có phải là thẻ gốc không
  bool get isParentCard => parentCardId == null;
}