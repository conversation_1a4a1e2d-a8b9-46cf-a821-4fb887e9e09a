class EventCard {
  final String id;
  final int chapter;
  final String title;
  final String character;
  final String description;
  final String image;
  final String leftText;
  final String rightText;
  final Map<String, int> leftEffect;
  final Map<String, int> rightEffect;

  EventCard({
    required this.id,
    required this.chapter,
    required this.title,
    required this.character,
    required this.description,
    required this.image,
    required this.leftText,
    required this.rightText,
    required this.leftEffect,
    required this.rightEffect,
  });

  factory EventCard.fromJson(Map<String, dynamic> json) {
    return EventCard(
      id: json['id'],
      chapter: json['chapter'],
      title: json['title'],
      character: json['character'],
      description: json['description'],
      image: json['image'],
      leftText: json['leftText'],
      rightText: json['rightText'],
      leftEffect: Map<String, int>.from(json['leftEffect']),
      rightEffect: Map<String, int>.from(json['rightEffect']),
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'chapter': chapter,
    'character': character,
    'description': description,
    'image': image,
    'leftText': leftText,
    'rightText': rightText,
    'leftEffect': leftEffect,
    'rightEffect': rightEffect,
  };
}
