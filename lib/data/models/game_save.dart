import 'package:hive/hive.dart';

part 'game_save.g.dart'; // Chạy build_runner sau khi viết file này

@HiveType(typeId: 0)
class GameSave extends HiveObject {
  @HiveField(0)
  int internalTrust;

  @HiveField(1)
  int externalTrust;

  @HiveField(2)
  int mentalStability;

  @HiveField(3)
  int socialLinks;

  @HiveField(4)
  int currentChapter;

  @HiveField(5)
  int missionSentCount;

  GameSave({
    required this.internalTrust,
    required this.externalTrust,
    required this.mentalStability,
    required this.socialLinks,
    required this.currentChapter,
    required this.missionSentCount,
  });

  @override
  String toString() {
    return 'GameSave(internalTrust: $internalTrust, externalTrust: $externalTrust, mentalStability: $mentalStability, socialLinks: $socialLinks, currentChapter: $currentChapter, missionSentCount: $missionSentCount)';
  }
}
