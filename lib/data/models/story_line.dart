class StoryLine {
  final int? id;
  final String? name;
  final String? description;
  final int isUnlocked;

  StoryLine({
    this.id,
    this.name,
    this.description,
    required this.isUnlocked,
  });

  factory StoryLine.fromMap(Map<String, dynamic> map) {
    return StoryLine(
      id: map['id']?.toInt(),
      name: map['name'],
      description: map['description'],
      isUnlocked: map['isUnlocked']?.toInt() ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'isUnlocked': isUnlocked,
    };
  }

  @override
  String toString() {
    return 'StoryLine{id: $id, name: $name, description: $description, isUnlocked: $isUnlocked}';
  }
}
