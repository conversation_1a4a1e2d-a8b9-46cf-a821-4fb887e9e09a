import '../database/database_helper.dart';
import '../models/card.dart';
import '../models/character.dart';

class CardRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;

  // Helper method to create Card object with character info
  Card _createCardFromMap(Map<String, dynamic> cardMap, Map<String, dynamic>? characterMap) {
    Character? character;
    if (characterMap != null) {
      character = Character(
        id: characterMap['id'] as int,
        name: characterMap['name'] as String,
        imgUrl: characterMap['image'] as String,
        description: characterMap['description'] as String,
        isUnlocked: characterMap['isUnlocked'] as int,
      );
    }

    return Card(
      id: cardMap['id'] as String,
      characterId: cardMap['characterId'] as int?,
      character: character,
      description: cardMap['description'] as String,
      type: cardMap['type'] as String,
      leftEffect: cardMap['leftEffect'] as String,
      rightEffect: cardMap['rightEffect'] as String,
      leftText: cardMap['leftText'] as String,
      rightText: cardMap['rightText'] as String,
      parentCardId: cardMap['parent_card_id'] as String?,
      unlockId: cardMap['unlockId']?.toString(),
      isDisable: cardMap['isDisable'] as int? ?? 0,
    );
  }

  // Get all cards with type "intro" with character info
  Future<List<Card>> getIntroCards() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT 
        c.id, c.characterId, c.description, c.type, c.leftEffect, c.rightEffect, c.leftText, c.rightText, c.parent_card_id, c.unlockId, c.isDisable,
        ch.id as char_id, ch.name, ch.image, ch.description as char_description, ch.isUnlocked
      FROM card c
      LEFT JOIN character ch ON c.characterId = ch.id
      WHERE c.type = ?
    ''', ['intro']);

    return maps.map((map) {
      Map<String, dynamic>? characterMap;
      if (map['char_id'] != null) {
        characterMap = {
          'id': map['char_id'],
          'name': map['name'],
          'image': map['image'],
          'description': map['char_description'],
          'isUnlocked': map['isUnlocked'],
        };
      }

      final cardMap = {
        'id': map['id'],
        'characterId': map['characterId'],
        'description': map['description'],
        'type': map['type'],
        'leftEffect': map['leftEffect'],
        'rightEffect': map['rightEffect'],
        'leftText': map['leftText'],
        'rightText': map['rightText'],
        'parent_card_id': map['parent_card_id'],
        'unlockId': map['unlockId'],
        'isDisable': map['isDisable'],
      };

      return _createCardFromMap(cardMap, characterMap);
    }).toList();
  }

  // Get all cards with type "normal" and no parent (chỉ lấy thẻ gốc và không bị disable)
  Future<List<Card>> getNormalCards() async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT 
        c.id, c.characterId, c.description, c.type, c.leftEffect, c.rightEffect, c.leftText, c.rightText, c.parent_card_id, c.unlockId, c.isDisable,
        ch.id as char_id, ch.name, ch.image, ch.description as char_description, ch.isUnlocked
      FROM card c
      LEFT JOIN character ch ON c.characterId = ch.id
      WHERE c.type = ? AND c.parent_card_id IS NULL AND (ch.isUnlocked = 1 OR c.characterId IS NULL) AND (c.isDisable = 0 OR c.isDisable IS NULL)
    ''', ['normal']);

    return maps.map((map) {
      Map<String, dynamic>? characterMap;
      if (map['char_id'] != null) {
        characterMap = {
          'id': map['char_id'],
          'name': map['name'],
          'image': map['image'],
          'description': map['char_description'],
          'isUnlocked': map['isUnlocked'],
        };
      }

      final cardMap = {
        'id': map['id'],
        'characterId': map['characterId'],
        'description': map['description'],
        'type': map['type'],
        'leftEffect': map['leftEffect'],
        'rightEffect': map['rightEffect'],
        'leftText': map['leftText'],
        'rightText': map['rightText'],
        'parent_card_id': map['parent_card_id'],
        'unlockId': map['unlockId'],
        'isDisable': map['isDisable'],
      };

      return _createCardFromMap(cardMap, characterMap);
    }).toList();
  }

  // Lấy thẻ con dựa trên parent card ID và lựa chọn (Y hoặc N)
  Future<Card?> getChildCard(String parentCardId, bool isLeftChoice) async {
    final db = await _databaseHelper.database;

    // Tạo child card ID dựa trên parent ID và lựa chọn
    final childCardId = parentCardId + (isLeftChoice ? 'L' : 'R');

    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT 
        c.id, c.characterId, c.description, c.type, c.leftEffect, c.rightEffect, c.leftText, c.rightText, c.parent_card_id,
        ch.id as char_id, ch.name, ch.image, ch.description as char_description, ch.isUnlocked
      FROM card c
      LEFT JOIN character ch ON c.characterId = ch.id
      WHERE c.id = ?
    ''', [childCardId]);

    if (maps.isEmpty) {
      return null;
    }

    final map = maps.first;
    Map<String, dynamic>? characterMap;
    if (map['char_id'] != null) {
      characterMap = {
        'id': map['char_id'],
        'name': map['name'],
        'image': map['image'],
        'description': map['char_description'],
        'isUnlocked': map['isUnlocked'],
      };
    }

    final cardMap = {
      'id': map['id'],
      'characterId': map['characterId'],
      'description': map['description'],
      'type': map['type'],
      'leftEffect': map['leftEffect'],
      'rightEffect': map['rightEffect'],
      'leftText': map['leftText'],
      'rightText': map['rightText'],
      'parent_card_id': map['parent_card_id'],
    };

    return _createCardFromMap(cardMap, characterMap);
  }

  // Kiểm tra xem một thẻ có thẻ con không
  Future<bool> hasChildCards(String cardId) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT COUNT(*) as count
      FROM card
      WHERE parent_card_id = ?
    ''', [cardId]);

    return (maps.first['count'] as int) > 0;
  }

  // Kiểm tra xem một thẻ có thẻ con L không
  Future<bool> hasLeftChild(String cardId) async {
    final db = await _databaseHelper.database;
    final leftChildId = cardId + 'L';
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT COUNT(*) as count
      FROM card
      WHERE id = ?
    ''', [leftChildId]);

    return (maps.first['count'] as int) > 0;
  }

  // Kiểm tra xem một thẻ có thẻ con R không
  Future<bool> hasRightChild(String cardId) async {
    final db = await _databaseHelper.database;
    final rightChildId = cardId + 'R';
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT COUNT(*) as count
      FROM card
      WHERE id = ?
    ''', [rightChildId]);

    return (maps.first['count'] as int) > 0;
  }

  // Get a single card with specific death type
  Future<Card?> getDeathCards(String deathType) async {
    final db = await _databaseHelper.database;

    // Map death types to specific card types
    String cardType;
    switch (deathType) {
      case 'internal':
        cardType = 'internalDeath';
        break;
      case 'external':
        cardType = 'externalDeath';
        break;
      case 'mental':
        cardType = 'mentalDeath';
        break;
      case 'social':
        cardType = 'socialDeath';
        break;
      default:
        return null;
    }

    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT 
        c.id, c.characterId, c.description, c.type, c.leftEffect, c.rightEffect, c.leftText, c.rightText, c.parent_card_id,
        ch.id as char_id, ch.name, ch.image, ch.description as char_description, ch.isUnlocked
      FROM card c
      LEFT JOIN character ch ON c.characterId = ch.id
      WHERE c.type = ?
      LIMIT 1
    ''', [cardType]);

    if (maps.isEmpty) {
      return null;
    }

    final map = maps.first;
    Map<String, dynamic>? characterMap;
    if (map['char_id'] != null) {
      characterMap = {
        'id': map['char_id'],
        'name': map['name'],
        'image': map['image'],
        'description': map['char_description'],
        'isUnlocked': map['isUnlocked'],
      };
    }

    final cardMap = {
      'id': map['id'],
      'characterId': map['characterId'],
      'description': map['description'],
      'type': map['type'],
      'leftEffect': map['leftEffect'],
      'rightEffect': map['rightEffect'],
      'leftText': map['leftText'],
      'rightText': map['rightText'],
      'parent_card_id': map['parent_card_id'],
    };

    return _createCardFromMap(cardMap, characterMap);
  }

  Future<List<Card>> getDeathType(String specificDeathType) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT
        c.id, c.characterId, c.description, c.type, c.leftEffect, c.rightEffect, c.leftText, c.rightText, c.parent_card_id,
        ch.id as char_id, ch.name, ch.image, ch.description as char_description, ch.isUnlocked
      FROM card c
      LEFT JOIN character ch ON c.characterId = ch.id
      WHERE c.type = ?
    ''', [specificDeathType]);

    return maps.map((map) {
      Map<String, dynamic>? characterMap;
      if (map['char_id'] != null) {
        characterMap = {
          'id': map['char_id'],
          'name': map['name'],
          'image': map['image'],
          'description': map['char_description'],
          'isUnlocked': map['isUnlocked'],
        };
      }

      final cardMap = {
        'id': map['id'],
        'characterId': map['characterId'],
        'description': map['description'],
        'type': map['type'],
        'leftEffect': map['leftEffect'],
        'rightEffect': map['rightEffect'],
        'leftText': map['leftText'],
        'rightText': map['rightText'],
        'parent_card_id': map['parent_card_id'],
      };

      return _createCardFromMap(cardMap, characterMap);
    }).toList();
  }

  // Get item card based on death type
  Future<Card?> getItemCard(String deathType) async {
    final db = await _databaseHelper.database;

    // Map death types to item card types
    String itemCardType;
    switch (deathType) {
      case 'internal':
        itemCardType = 'item_eye';
        break;
      case 'external':
        itemCardType = 'item_badge';
        break;
      case 'mental':
        itemCardType = 'item_heart';
        break;
      case 'social':
        itemCardType = 'item_newspaper';
        break;
      default:
        return null;
    }

    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT 
        c.id, c.characterId, c.description, c.type, c.leftEffect, c.rightEffect, c.leftText, c.rightText, c.parent_card_id,
        ch.id as char_id, ch.name, ch.image, ch.description as char_description, ch.isUnlocked
      FROM card c
      LEFT JOIN character ch ON c.characterId = ch.id
      WHERE c.type = ?
      LIMIT 1
    ''', [itemCardType]);

    if (maps.isEmpty) {
      return null;
    }

    final map = maps.first;
    Map<String, dynamic>? characterMap;
    if (map['char_id'] != null) {
      characterMap = {
        'id': map['char_id'],
        'name': map['name'],
        'image': map['image'],
        'description': map['char_description'],
        'isUnlocked': map['isUnlocked'],
      };
    }

    final cardMap = {
      'id': map['id'],
      'characterId': map['characterId'],
      'description': map['description'],
      'type': map['type'],
      'leftEffect': map['leftEffect'],
      'rightEffect': map['rightEffect'],
      'leftText': map['leftText'],
      'rightText': map['rightText'],
      'parent_card_id': map['parent_card_id'],
    };

    return _createCardFromMap(cardMap, characterMap);
  }

  // Unlock a character by setting isUnlocked = 1
  Future<void> unlockCharacter(String characterId) async {
    final db = await _databaseHelper.database;
    await db.rawUpdate('''
      UPDATE character 
      SET isUnlocked = 1 
      WHERE id = ?
    ''', [characterId]);
  }

  // Disable a card by setting isDisable = 1
  Future<void> disableCard(String cardId) async {
    final db = await _databaseHelper.database;
    await db.rawUpdate('''
      UPDATE card 
      SET isDisable = 1 
      WHERE id = ?
    ''', [cardId]);
  }
}