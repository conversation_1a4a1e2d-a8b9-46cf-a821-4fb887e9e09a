import 'package:spy/data/models/character.dart';
import 'package:spy/data/database/database_helper.dart';

class CharacterRepository {
  final DatabaseHelper _databaseHelper;

  CharacterRepository(this._databaseHelper);

  // L<PERSON>y ra số lượng bản ghi trong bảng character
  Future<int> getCharacterCount() async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.rawQuery('SELECT COUNT(*) as count FROM character');
      return result.first['count'] as int;
    } catch (e) {
      print('Error getting character count: $e');
      return 0;
    }
  }

  // Lấy ra tất cả các nhân vật trong bảng
  Future<List<Character>> getAllCharacters() async {
    try {
      final db = await _databaseHelper.database;
      final maps = await db.query('character');

      return List.generate(maps.length, (i) {
        return Character(
          id: maps[i]['id'] as int,
          name: maps[i]['name'] as String,
          imgUrl: maps[i]['image'] as String, // Sử dụng 'image' thay vì 'imgUrl' theo DB schema
          description: maps[i]['description'] as String,
          isUnlocked: maps[i]['isUnlocked'] as int,
        );
      });
    } catch (e) {
      print('Error getting all characters: $e');
      return [];
    }
  }

  // Lấy ra nhân vật theo id
  Future<Character?> getCharacterById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final maps = await db.query(
        'character',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return Character(
          id: maps.first['id'] as int,
          name: maps.first['name'] as String,
          imgUrl: maps.first['image'] as String,
          description: maps.first['description'] as String,
          isUnlocked: maps.first['isUnlocked'] as int,
        );
      }
      return null;
    } catch (e) {
      print('Error getting character by id: $e');
      return null;
    }
  }

  // Lấy ra các nhân vật đã mở khóa
  Future<List<Character>> getUnlockedCharacters() async {
    try {
      final db = await _databaseHelper.database;
      final maps = await db.query(
        'character',
        where: 'isUnlocked = ?',
        whereArgs: [1],
      );

      return List.generate(maps.length, (i) {
        return Character(
          id: maps[i]['id'] as int,
          name: maps[i]['name'] as String,
          imgUrl: maps[i]['image'] as String,
          description: maps[i]['description'] as String,
          isUnlocked: maps[i]['isUnlocked'] as int,
        );
      });
    } catch (e) {
      print('Error getting unlocked characters: $e');
      return [];
    }
  }

  // Mở khóa nhân vật theo id
  Future<void> unlockCharacter(int characterId) async {
    try {
      final db = await _databaseHelper.database;
      await db.update(
        'character',
        {'isUnlocked': 1},
        where: 'id = ?',
        whereArgs: [characterId],
      );
      print('Character $characterId unlocked successfully');
    } catch (e) {
      print('Error unlocking character $characterId: $e');
      throw e;
    }
  }

}
