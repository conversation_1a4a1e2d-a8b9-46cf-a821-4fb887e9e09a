import 'package:spy/data/models/character.dart';
import 'package:spy/data/database/database_helper.dart';

class CharacterRepository {
  final DatabaseHelper _databaseHelper;

  CharacterRepository(this._databaseHelper);

  // ignore: non_constant_identifier_names
  Future<Character?> CharacterById(int id) async {
    final db = await _databaseHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'characters',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Character(
        id: maps.first['id'] as int,
        name: maps.first['name'] as String,
        imgUrl: maps.first['image'] as String,
        description: maps.first['description'] as String,
        isUnlocked: maps.first['isUnlocked'] as int,
      );
    }

    return null;
  }
}
