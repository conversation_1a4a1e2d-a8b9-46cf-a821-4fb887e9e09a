import 'dart:convert';
import 'package:flutter/services.dart';
import '../models/event_card.dart';

class EventRepository {
  Future<List<EventCard>> loadByChapter(int chapter) async {
    final jsonString =
        await rootBundle.loadString('lib/data/source/chapter_$chapter.json');
    final List<dynamic> jsonList = json.decode(jsonString);

    return jsonList
        .map((json) => EventCard.fromJson(json))
        .where((card) => card.chapter == chapter)
        .toList();
  }

  Future<List<EventCard>> loadAll() async {
    // Danh sách chương bạn có thể điều chỉnh nếu biết rõ
    final List<int> chapterList = [1, 2, 3]; // sửa theo số chương thật
    return await loadAllChapters(chapterList);
  }

  Future<List<EventCard>> loadAllChapters(List<int> chapterList) async {
    List<EventCard> allCards = [];
    for (final chapter in chapterList) {
      final cards = await loadByChapter(chapter);
      allCards.addAll(cards);
    }
    return allCards;
  }

  Future<EventCard> loadById(String id) async {
    final allCards = await loadAll();
    return allCards.firstWhere((card) => card.id == id);
  }

  Map<String, int> getCardsPreviewEffect(EventCard card, bool isLeft) {
    return isLeft ? card.leftEffect : card.rightEffect;
  }

  // Nếu bạn muốn lấy tổng số chapter từ JSON:
  Future<int> getChapterCount() async {
    final jsonString =
        await rootBundle.loadString('lib/data/source/chapter_1.json');
    final jsonList = json.decode(jsonString);
    return jsonList.length; // hoặc logic khác nếu bạn có trường 'count'
  }

  Future<EventCard?> loadDeathCardByChapter(
      int chapter, String deathType) async {
    final cards = await loadByChapter(chapter);
    return cards.firstWhere(
      (c) => c.id.toLowerCase().startsWith('${deathType.toLowerCase()}death'),
    );
  }

  Future<EventCard?> loadItemCardByChapter(
      int chapter, String itemType) async {
    final cards = await loadByChapter(chapter);
    return cards.firstWhere(
      (c) => c.id.toLowerCase().startsWith('item_${itemType.toLowerCase()}'),
    );
  }
}
