import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../models/story_line.dart';

class StoryRepository {
  static final StoryRepository instance = StoryRepository._init();
  StoryRepository._init();

  // Get total number of records in story_line table
  Future<int> getTotalCount() async {
    final db = await DatabaseHelper.instance.database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM story_line');
    return Sqflite.firstIntValue(result) ?? 0;
  }

  // Get all story line records
  Future<List<StoryLine>> getAllStoryLines() async {
    final db = await DatabaseHelper.instance.database;
    final result = await db.query('story_line', orderBy: 'id ASC');
    return result.map((map) => StoryLine.fromMap(map)).toList();
  }

  // Get number of unlocked story lines (isUnlocked = 1)
  Future<int> getUnlockedCount() async {
    final db = await DatabaseHelper.instance.database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM story_line WHERE isUnlocked = 1');
    return Sqflite.firstIntValue(result) ?? 0;
  }

  // Get only unlocked story lines
  Future<List<StoryLine>> getUnlockedStoryLines() async {
    final db = await DatabaseHelper.instance.database;
    final result = await db.query('story_line', where: 'isUnlocked = ?', whereArgs: [1], orderBy: 'id ASC');
    return result.map((map) => StoryLine.fromMap(map)).toList();
  }

  // Get a specific story line by id
  Future<StoryLine?> getStoryLineById(int id) async {
    final db = await DatabaseHelper.instance.database;
    final result = await db.query('story_line', where: 'id = ?', whereArgs: [id]);
    if (result.isNotEmpty) {
      return StoryLine.fromMap(result.first);
    }
    return null;
  }

  // Update unlock status of a story line
  Future<int> updateUnlockStatus(int id, bool isUnlocked) async {
    final db = await DatabaseHelper.instance.database;
    return await db.update(
      'story_line',
      {'isUnlocked': isUnlocked ? 1 : 0},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Unlock a story line by id
  Future<void> unlockStoryLine(int id) async {
    await updateUnlockStatus(id, true);
  }
}
