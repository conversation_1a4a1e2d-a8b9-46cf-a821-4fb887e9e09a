import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import '../../data/models/card.dart' as game_card;
import '../../data/repositories/card_repository.dart';
import '../service/effect_service.dart';
import '../service/score_service.dart';
import '../service/story_service.dart';
import '../service/character_service.dart';
import 'game_state.dart';
import 'dart:math';

class GameCubit extends Cubit<GameState> {
  final CardRepository repository;
  final ScoreService scoreService;
  final StoryService storyService;
  final CharacterService characterService;
  final Random _random = Random();

  // Constants
  static const double swipeThreshold = 80.0;

  GameCubit(this.repository, this.scoreService, this.storyService, this.characterService) : super(GameInitial());

  Future<void> startGame() async {
    await scoreService.init();
    await storyService.init(); // Initialize story service

    // Fetch cards from the repository
    final introCards = await repository.getIntroCards();
    final normalCards = await repository.getNormalCards();

    // Shuffle normal cards để random
    normalCards.shuffle(_random);

    if (introCards.isNotEmpty) {
      emit(GamePlaying(
        currentCard: introCards.first,
        remainingCards: normalCards,
        internalTrust: 5,
        externalTrust: 5,
        mentalStability: 5,
        socialLinks: 5,
      ));
    } else {
      emit(GameOver("No cards available to start the game."));
    }
  }

  // Swipe handling methods - tách logic ra khỏi UI
  void updateCardPosition(Offset delta) {
    if (state is! GamePlaying) return;

    final currentState = state as GamePlaying;
    final newOffset = currentState.cardOffset + delta;
    final card = currentState.currentCard;

    String newSwipeText = "";
    if (newOffset.dx > 20) {
      newSwipeText = card.rightText;
    } else if (newOffset.dx < -20) {
      newSwipeText = card.leftText;
    }

    emit(currentState.copyWith(
      cardOffset: newOffset,
      swipeText: newSwipeText,
    ));
  }

  void handleSwipeEnd(double screenWidth) {
    if (state is! GamePlaying) return;

    final currentState = state as GamePlaying;
    final cardOffset = currentState.cardOffset;

    final isSwipeRight = cardOffset.dx > swipeThreshold;
    final isSwipeLeft = cardOffset.dx < -swipeThreshold;

    if (isSwipeRight || isSwipeLeft) {
      final endOffset = Offset(
        isSwipeRight ? screenWidth * 1.5 : -screenWidth * 1.5,
        cardOffset.dy,
      );

      emit(currentState.copyWith(
        isCardAnimatingOut: true,
        swipeOutRotation: cardOffset.dx * 0.002,
        shouldStartSwipeAnimation: true,
        swipeEndOffset: endOffset,
      ));
    } else {
      // Start reset animation
      emit(currentState.copyWith(
        shouldStartResetAnimation: true,
      ));
    }
  }

  void onSwipeAnimationUpdate(Offset animationOffset) {
    if (state is! GamePlaying) return;

    final currentState = state as GamePlaying;
    emit(currentState.copyWith(
      cardOffset: animationOffset,
      shouldStartSwipeAnimation: false,
    ));
  }

  void onResetAnimationUpdate(Offset animationOffset) {
    if (state is! GamePlaying) return;

    final currentState = state as GamePlaying;
    emit(currentState.copyWith(
      cardOffset: animationOffset,
      shouldStartResetAnimation: false,
    ));
  }

  void onSwipeAnimationCompleted() {
    if (state is! GamePlaying) return;

    final currentState = state as GamePlaying;
    final isLeft = currentState.cardOffset.dx < 0;

    // Reset animation flags and choose card
    emit(currentState.copyWith(
      cardOffset: Offset.zero,
      swipeText: "",
      swipeOutRotation: 0.0,
      isCardAnimatingOut: false,
      shouldStartSwipeAnimation: false,
    ));

    chooseCard(isLeft: isLeft);
  }

  void onResetAnimationCompleted() {
    if (state is! GamePlaying) return;

    final currentState = state as GamePlaying;
    emit(currentState.copyWith(
      cardOffset: Offset.zero,
      swipeText: "",
      shouldStartResetAnimation: false,
    ));
  }

  Future<void> chooseCard({required bool isLeft}) async {
    if (state is! GamePlaying) return;

    final gameState = state as GamePlaying;
    final card = gameState.currentCard;

    // Xử lý thẻ item
    if (_isItemCard(card)) {
      await _handleItemCard(gameState, card);
      return;
    }

    // Xử lý thẻ death
    if (_isDeathCard(card)) {
      await _handleDeathCard(gameState, card);
      return;
    }

    // Xử lý thẻ b��nh thường
    await _handleNormalCard(gameState, card, isLeft);
  }

  bool _isItemCard(game_card.Card card) {
    return card.type == 'item_eye' ||
        card.type == 'item_badge' ||
        card.type == 'item_heart' ||
        card.type == 'item_newspaper';
  }

  Future<void> _handleItemCard(GamePlaying state, game_card.Card card) async {
    final updatedStats = _applyItemEffect(state, card);

    if (_isItemFromDeath(state)) {
      final deathType = _determineDeathTypeFromItem(card.type);
      final recoveryStats = _applyDeathRecovery(state, deathType);
      await _proceedToNextCard(recoveryStats, state.remainingCards);
    } else {
      await _proceedToNextCard(updatedStats, state.remainingCards);
    }
  }

  bool _isItemFromDeath(GamePlaying state) {
    return state.internalTrust <= 0 ||
        state.externalTrust <= 0 ||
        state.mentalStability <= 0 ||
        state.socialLinks <= 0;
  }

  String _determineDeathTypeFromItem(String itemType) {
    switch (itemType) {
      case 'item_eye': return 'internal';
      case 'item_badge': return 'external';
      case 'item_heart': return 'mental';
      case 'item_newspaper': return 'social';
      default: return '';
    }
  }

  Map<String, int> _applyItemEffect(GamePlaying state, game_card.Card card) {
    int newTrust = state.internalTrust;
    int newExternal = state.externalTrust;
    int newMental = state.mentalStability;
    int newSocial = state.socialLinks;

    switch (card.type) {
      case 'item_eye': newTrust = (state.internalTrust + 3).clamp(0, 10); break;
      case 'item_badge': newExternal = (state.externalTrust + 3).clamp(0, 10); break;
      case 'item_heart': newMental = (state.mentalStability + 3).clamp(0, 10); break;
      case 'item_newspaper': newSocial = (state.socialLinks + 3).clamp(0, 10); break;
    }

    return {
      'internalTrust': newTrust,
      'externalTrust': newExternal,
      'mentalStability': newMental,
      'socialLinks': newSocial,
    };
  }

  bool _isDeathCard(game_card.Card card) {
    return card.type == 'internalDeath' ||
        card.type == 'externalDeath' ||
        card.type == 'mentalDeath' ||
        card.type == 'socialDeath';
  }

  Future<void> _handleDeathCard(GamePlaying state, game_card.Card card) async {
    final deathType = _getDeathType(card);
    final effectService = EffectService(scoreService);
    await effectService.init();
    final hasItem = await effectService.tryUseItemForDeath(deathType);

    if (hasItem) {
      final itemCard = await repository.getItemCard(deathType);
      if (itemCard != null) {
        emit(GamePlaying(
          internalTrust: state.internalTrust,
          externalTrust: state.externalTrust,
          mentalStability: state.mentalStability,
          socialLinks: state.socialLinks,
          currentCard: itemCard,
          remainingCards: state.remainingCards,
        ));
      } else {
        final updatedStats = _applyDeathRecovery(state, deathType);
        await _proceedToNextCard(updatedStats, state.remainingCards);
      }
    } else {
      await scoreService.handleGameOver();
      emit(GameOver("Bạn đã chết vì một chỉ số tụt về 0!"));
    }
  }

  String _getDeathType(game_card.Card card) {
    if (card.type == 'internalDeath') return 'internal';
    if (card.type == 'externalDeath') return 'external';
    if (card.type == 'mentalDeath') return 'mental';
    if (card.type == 'socialDeath') return 'social';
    return '';
  }

  Map<String, int> _applyDeathRecovery(GamePlaying state, String deathType) {
    int newTrust = state.internalTrust;
    int newExternal = state.externalTrust;
    int newMental = state.mentalStability;
    int newSocial = state.socialLinks;

    switch (deathType) {
      case 'internal': newTrust = 3; break;
      case 'external': newExternal = 3; break;
      case 'mental': newMental = 3; break;
      case 'social': newSocial = 3; break;
    }

    return {
      'internalTrust': newTrust,
      'externalTrust': newExternal,
      'mentalStability': newMental,
      'socialLinks': newSocial,
    };
  }

  Future<void> _proceedToNextCard(Map<String, int> updatedStats, List<game_card.Card> remainingCards) async {
    if (remainingCards.isEmpty) {
      emit(GameWin("Chúc mừng! Bạn đã hoàn thành nhiệm vụ!"));
      return;
    }

    final nextCard = remainingCards.first;
    final newRemainingCards = remainingCards.skip(1).toList();

    final newTrust = updatedStats['internalTrust']!;
    final newExternal = updatedStats['externalTrust']!;
    final newMental = updatedStats['mentalStability']!;
    final newSocial = updatedStats['socialLinks']!;

    if (newTrust <= 0 || newExternal <= 0 || newMental <= 0 || newSocial <= 0) {
      String deathType = '';
      if (newTrust <= 0) deathType = 'internal';
      else if (newExternal <= 0) deathType = 'external';
      else if (newMental <= 0) deathType = 'mental';
      else if (newSocial <= 0) deathType = 'social';

      final deathCard = await repository.getDeathCards(deathType);
      if (deathCard != null) {
        emit(GamePlaying(
          currentCard: deathCard,
          remainingCards: [nextCard, ...newRemainingCards],
          internalTrust: newTrust,
          externalTrust: newExternal,
          mentalStability: newMental,
          socialLinks: newSocial,
        ));
      } else {
        await scoreService.handleGameOver();
        emit(GameOver("Bạn đã chết vì một chỉ số tụt về 0!"));
      }
    } else {
      emit(GamePlaying(
        currentCard: nextCard,
        remainingCards: newRemainingCards,
        internalTrust: newTrust,
        externalTrust: newExternal,
        mentalStability: newMental,
        socialLinks: newSocial,
      ));
    }
  }

  Future<void> _handleNormalCard(GamePlaying state, game_card.Card card, bool isLeft) async {
    final childCard = await repository.getChildCard(card.id, isLeft);

    if (childCard != null) {
      final effectMap = isLeft ? card.leftEffectMap : card.rightEffectMap;
      final updatedStats = _calculateUpdatedStats(state, effectMap);

      if (_shouldShowDeathCard(updatedStats)) {
        await _showDeathCardBeforeChild(updatedStats, childCard, state.remainingCards);
      } else {
        emit(GamePlaying(
          currentCard: childCard,
          remainingCards: state.remainingCards,
          internalTrust: updatedStats['internalTrust']!,
          externalTrust: updatedStats['externalTrust']!,
          mentalStability: updatedStats['mentalStability']!,
          socialLinks: updatedStats['socialLinks']!,
        ));
      }
    } else {
      final effectMap = isLeft ? card.leftEffectMap : card.rightEffectMap;
      final updatedStats = _calculateUpdatedStats(state, effectMap);
      await _proceedToNextCard(updatedStats, state.remainingCards);
    }
  }

  Map<String, int> _calculateUpdatedStats(GamePlaying state, Map<String, int> effectMap) {
    int newTrust = (state.internalTrust + (effectMap['internalTrust'] ?? 0)).clamp(0, 10);
    int newExternal = (state.externalTrust + (effectMap['externalTrust'] ?? 0)).clamp(0, 10);
    int newMental = (state.mentalStability + (effectMap['mentalStability'] ?? 0)).clamp(0, 10);
    int newSocial = (state.socialLinks + (effectMap['socialLinks'] ?? 0)).clamp(0, 10);

    return {
      'internalTrust': newTrust,
      'externalTrust': newExternal,
      'mentalStability': newMental,
      'socialLinks': newSocial,
    };
  }

  bool _shouldShowDeathCard(Map<String, int> stats) {
    return stats['internalTrust']! <= 0 ||
        stats['externalTrust']! <= 0 ||
        stats['mentalStability']! <= 0 ||
        stats['socialLinks']! <= 0;
  }

  Future<void> _showDeathCardBeforeChild(Map<String, int> stats, game_card.Card childCard, List<game_card.Card> remainingCards) async {
    String deathType = '';
    if (stats['internalTrust']! <= 0) deathType = 'internal';
    else if (stats['externalTrust']! <= 0) deathType = 'external';
    else if (stats['mentalStability']! <= 0) deathType = 'mental';
    else if (stats['socialLinks']! <= 0) deathType = 'social';

    final deathCard = await repository.getDeathCards(deathType);
    if (deathCard != null) {
      emit(GamePlaying(
        currentCard: deathCard,
        remainingCards: [childCard, ...remainingCards],
        internalTrust: stats['internalTrust']!,
        externalTrust: stats['externalTrust']!,
        mentalStability: stats['mentalStability']!,
        socialLinks: stats['socialLinks']!,
      ));
    } else {
      await scoreService.handleGameOver();
      emit(GameOver("Bạn đã chết vì một chỉ số tụt về 0!"));
    }
  }
}
