import 'package:flutter_bloc/flutter_bloc.dart';
import '../../data/models/card.dart';
import '../../data/repositories/card_repository.dart';
import '../service/effect_service.dart';
import '../service/score_service.dart';
import 'game_state.dart';
import 'dart:math';

class GameCubit extends Cubit<GameState> {
  final CardRepository repository;
  final ScoreService scoreService;
  final Random _random = Random();

  GameCubit(this.repository, this.scoreService) : super(GameInitial());

  Future<void> startGame() async {
    await scoreService.init();

    // Fetch cards from the repository
    final introCards = await repository.getIntroCards();
    final normalCards = await repository.getNormalCards();

    // Shuffle normal cards để random
    normalCards.shuffle(_random);

    if (introCards.isNotEmpty) {
      emit(GamePlaying(
        currentCard: introCards.first,
        remainingCards: normalCards,
        internalTrust: 5,
        externalTrust: 5,
        mentalStability: 5,
        socialLinks: 5,
      ));
    } else {
      emit(GameOver("No cards available to start the game."));
    }
  }

  void previewCard({required bool isLeft}) {
    if (state is! GamePlaying) return;

    final playingState = state as GamePlaying;
    final card = playingState.currentCard;
    final effectMap = isLeft ? card.leftEffectMap : card.rightEffectMap;

    emit(GameCardPreviewing(
      internalTrust: playingState.internalTrust,
      externalTrust: playingState.externalTrust,
      mentalStability: playingState.mentalStability,
      socialLinks: playingState.socialLinks,
      currentCard: playingState.currentCard,
      remainingCards: playingState.remainingCards,
      isLeft: isLeft,
      previewEffect: effectMap,
    ));
  }

  void cancelPreview() {
    if (state is GameCardPreviewing) {
      final current = state as GameCardPreviewing;
      emit(GamePlaying(
        currentCard: current.currentCard,
        internalTrust: current.internalTrust,
        externalTrust: current.externalTrust,
        mentalStability: current.mentalStability,
        socialLinks: current.socialLinks,
        remainingCards: current.remainingCards,
      ));
    }
  }

  Future<void> chooseCard({required bool isLeft}) async {
    if (state is! GamePlaying && state is! GameCardPreviewing) return;

    final s = state as GamePlaying;
    final card = s.currentCard;

    // Xử lý thẻ item
    if (_isItemCard(card)) {
      await _handleItemCard(s, card);
      return;
    }

    // Xử lý thẻ death
    if (_isDeathCard(card)) {
      await _handleDeathCard(s, card);
      return;
    }

    // Xử lý thẻ bình thường
    await _handleNormalCard(s, card, isLeft);
  }

  // Check if card is an item card
  bool _isItemCard(Card card) {
    return card.type == 'item_eye' ||
        card.type == 'item_badge' ||
        card.type == 'item_heart' ||
        card.type == 'item_newspaper';
  }

  // Handle item cards
  Future<void> _handleItemCard(GamePlaying state, Card card) async {
    final updatedStats = _applyItemEffect(state, card);

    if (_isItemFromDeath(state)) {
      final deathType = _determineDeathTypeFromItem(card.type);
      final recoveryStats = _applyDeathRecovery(state, deathType);
      await _proceedToNextCard(recoveryStats, state.remainingCards);
    } else {
      await _proceedToNextCard(updatedStats, state.remainingCards);
    }
  }

  // Kiểm tra xem thẻ item này có phải từ death không
  bool _isItemFromDeath(GamePlaying state) {
    return state.internalTrust <= 0 ||
        state.externalTrust <= 0 ||
        state.mentalStability <= 0 ||
        state.socialLinks <= 0;
  }

  // Xác định death type từ item type
  String _determineDeathTypeFromItem(String itemType) {
    switch (itemType) {
      case 'item_eye':
        return 'internal';
      case 'item_badge':
        return 'external';
      case 'item_heart':
        return 'mental';
      case 'item_newspaper':
        return 'social';
      default:
        return '';
    }
  }

  // Apply item effects to stats
  Map<String, int> _applyItemEffect(GamePlaying state, Card card) {
    int newTrust = state.internalTrust;
    int newExternal = state.externalTrust;
    int newMental = state.mentalStability;
    int newSocial = state.socialLinks;

    switch (card.type) {
      case 'item_eye':
        newTrust = (state.internalTrust + 3).clamp(0, 10);
        break;
      case 'item_badge':
        newExternal = (state.externalTrust + 3).clamp(0, 10);
        break;
      case 'item_heart':
        newMental = (state.mentalStability + 3).clamp(0, 10);
        break;
      case 'item_newspaper':
        newSocial = (state.socialLinks + 3).clamp(0, 10);
        break;
    }

    return {
      'internalTrust': newTrust,
      'externalTrust': newExternal,
      'mentalStability': newMental,
      'socialLinks': newSocial,
    };
  }

  // Check if card is a death card
  bool _isDeathCard(Card card) {
    return card.type == 'internalDeath' ||
        card.type == 'externalDeath' ||
        card.type == 'mentalDeath' ||
        card.type == 'socialDeath';
  }

  // Handle death cards
  Future<void> _handleDeathCard(GamePlaying state, Card card) async {
    final deathType = _getDeathType(card);
    final effectService = EffectService(scoreService);
    await effectService.init();
    final hasItem = await effectService.tryUseItemForDeath(deathType);

    if (hasItem) {
      // Lấy thẻ item tương ứng để hiển thị
      final itemCard = await repository.getItemCard(deathType);
      if (itemCard != null) {
        // Hiển thị thẻ item trước khi phục hồi
        emit(GamePlaying(
          internalTrust: state.internalTrust,
          externalTrust: state.externalTrust,
          mentalStability: state.mentalStability,
          socialLinks: state.socialLinks,
          currentCard: itemCard,
          remainingCards: state.remainingCards,
        ));
      } else {
        // Nếu không tìm thấy thẻ item, phục hồi luôn
        final updatedStats = _applyDeathRecovery(state, deathType);
        await _proceedToNextCard(updatedStats, state.remainingCards);
      }
    } else {
      await scoreService.handleGameOver();
      emit(GameOver("Bạn đã chết vì một chỉ số tụt về 0!"));
    }
  }

  // Get death type from card
  String _getDeathType(Card card) {
    if (card.type == 'internalDeath') {
      return 'internal';
    } else if (card.type == 'externalDeath') {
      return 'external';
    } else if (card.type == 'mentalDeath') {
      return 'mental';
    } else if (card.type == 'socialDeath') {
      return 'social';
    }
    return '';
  }

  // Apply death recovery (set stat to 3)
  Map<String, int> _applyDeathRecovery(GamePlaying state, String deathType) {
    int newTrust = state.internalTrust;
    int newExternal = state.externalTrust;
    int newMental = state.mentalStability;
    int newSocial = state.socialLinks;

    switch (deathType) {
      case 'internal':
        newTrust = 3;
        break;
      case 'external':
        newExternal = 3;
        break;
      case 'mental':
        newMental = 3;
        break;
      case 'social':
        newSocial = 3;
        break;
    }

    return {
      'internalTrust': newTrust,
      'externalTrust': newExternal,
      'mentalStability': newMental,
      'socialLinks': newSocial,
    };
  }

  // Apply card effects to stats
  Map<String, int> _applyCardEffect(GamePlaying state,
      Map<String, int> effect) {
    final newTrust = (state.internalTrust + (effect['internalTrust'] ?? 0))
        .clamp(0, 10);
    final newExternal = (state.externalTrust + (effect['externalTrust'] ?? 0))
        .clamp(0, 10);
    final newMental = (state.mentalStability + (effect['mentalStability'] ?? 0))
        .clamp(0, 10);
    final newSocial = (state.socialLinks + (effect['socialLinks'] ?? 0)).clamp(
        0, 10);

    return {
      'internalTrust': newTrust,
      'externalTrust': newExternal,
      'mentalStability': newMental,
      'socialLinks': newSocial,
    };
  }

  // Check if any stat has dropped to 0 or below
  bool _checkForDeath(Map<String, int> stats) {
    return stats['internalTrust']! <= 0 ||
        stats['externalTrust']! <= 0 ||
        stats['mentalStability']! <= 0 ||
        stats['socialLinks']! <= 0;
  }

  // Handle when a stat drops to 0
  Future<void> _handleStatDrop(GamePlaying state,
      Map<String, int> stats) async {
    final deathType = _determineDeathType(stats);
    final deathCard = await repository.getDeathCards(deathType);

    if (deathCard != null) {
      emit(GamePlaying(
        internalTrust: stats['internalTrust']!,
        externalTrust: stats['externalTrust']!,
        mentalStability: stats['mentalStability']!,
        socialLinks: stats['socialLinks']!,
        currentCard: deathCard,
        remainingCards: state.remainingCards,
      ));
    } else {
      await scoreService.handleGameOver();
      emit(GameOver("Không tìm thấy thẻ tử vong phù hợp!"));
    }
  }

  // Determine which stat caused death
  String _determineDeathType(Map<String, int> stats) {
    if (stats['internalTrust']! <= 0) return 'internal';
    if (stats['externalTrust']! <= 0) return 'external';
    if (stats['mentalStability']! <= 0) return 'mental';
    if (stats['socialLinks']! <= 0) return 'social';
    return '';
  }

  // Handle child cards
  Future<void> _handleChildCard(GamePlaying state, Card card, bool isLeft,
      Map<String, int> stats, Map<String, int> effect) async {
    final childCard = await repository.getChildCard(card.id, isLeft);

    if (childCard != null) {
      // Có thẻ con, hiển thị thẻ con
      _emitCardChosen(state, stats, isLeft, effect);
      emit(GamePlaying(
        internalTrust: stats['internalTrust']!,
        externalTrust: stats['externalTrust']!,
        mentalStability: stats['mentalStability']!,
        socialLinks: stats['socialLinks']!,
        currentCard: childCard,
        remainingCards: state.remainingCards,
      ));
    } else {
      // Không có thẻ con tương ứng, áp dụng hiệu ứng và quay về nhánh chính
      _emitCardChosen(state, stats, isLeft, effect);
      await _proceedToNextCard(stats, state.remainingCards);
    }
  }

  // Handle normal card progression
  Future<void> _handleCardProgression(GamePlaying state, Card card, bool isLeft,
      Map<String, int> stats, Map<String, int> effect) async {
    List<Card> nextCards = state.remainingCards;

    if (card.isChildCard) {
      // Child card finished, return to normal cards
      if (nextCards.isNotEmpty) {
        nextCards.shuffle(_random);
        _emitCardChosen(state, stats, isLeft, effect);
        await _proceedToNextCard(stats, nextCards);
      } else {
        await _handleGameComplete();
      }
    } else {
      // Normal card progression
      if (nextCards.isNotEmpty) {
        _emitCardChosen(state, stats, isLeft, effect);
        await _proceedToNextCard(stats, nextCards);
      } else {
        await _handleGameComplete();
      }
    }
  }

  // Emit GameCardChosen state
  void _emitCardChosen(GamePlaying state, Map<String, int> stats, bool isLeft,
      Map<String, int> effect) {
    emit(GameCardChosen(
      internalTrust: stats['internalTrust']!,
      externalTrust: stats['externalTrust']!,
      mentalStability: stats['mentalStability']!,
      socialLinks: stats['socialLinks']!,
      currentCard: state.currentCard,
      remainingCards: state.remainingCards,
      isLeft: isLeft,
      appliedEffect: effect,
    ));
  }

  // Proceed to next card
  Future<void> _proceedToNextCard(Map<String, int> stats,
      List<Card> remainingCards) async {
    if (remainingCards.isNotEmpty) {
      emit(GamePlaying(
        internalTrust: stats['internalTrust']!,
        externalTrust: stats['externalTrust']!,
        mentalStability: stats['mentalStability']!,
        socialLinks: stats['socialLinks']!,
        currentCard: remainingCards.first,
        remainingCards: remainingCards.sublist(1),
      ));
    } else {
      await _handleGameComplete();
    }
  }

  // Handle game completion
  Future<void> _handleGameComplete() async {
    await scoreService.handleGameOver();
    emit(GameOver("Bạn đã hoàn thành tất cả các thẻ!"));
  }

  // Handle normal cards with proper logic for cards that only have L or R children
  Future<void> _handleNormalCard(GamePlaying state, Card card,
      bool isLeft) async {
    // Kiểm tra xem có thẻ con cho hướng được chọn không
    final hasChildInDirection = isLeft
        ? await repository.hasLeftChild(card.id)
        : await repository.hasRightChild(card.id);

    // Áp dụng hiệu ứng của lựa chọn
    final effectMap = isLeft ? card.leftEffectMap : card.rightEffectMap;
    final updatedStats = _applyCardEffect(state, effectMap);

    if (_checkForDeath(updatedStats)) {
      await _handleStatDrop(state, updatedStats);
      return;
    }

    // Nếu có thẻ con cho hướng được chọn, chuyển đến thẻ con
    if (hasChildInDirection) {
      final childCard = await repository.getChildCard(card.id, isLeft);
      if (childCard != null) {
        emit(GamePlaying(
          internalTrust: updatedStats['internalTrust']!,
          externalTrust: updatedStats['externalTrust']!,
          mentalStability: updatedStats['mentalStability']!,
          socialLinks: updatedStats['socialLinks']!,
          currentCard: childCard,
          remainingCards: state.remainingCards,
        ));
        return;
      }
    }

    // Nếu không có thẻ con hoặc không tìm thấy thẻ con, tiếp tục với thẻ tiếp theo
    await _proceedToNextCard(updatedStats, state.remainingCards);
  }
}
