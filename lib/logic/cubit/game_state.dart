import 'package:equatable/equatable.dart';
import '../../data/models/card.dart';

abstract class GameState extends Equatable {
  @override
  List<Object?> get props => [];
}

class GameInitial extends GameState {}

class GamePlaying extends GameState {
  final int internalTrust;
  final int externalTrust;
  final int mentalStability;
  final int socialLinks;
  final Card currentCard;
  final List<Card> remainingCards;

  GamePlaying({
    required this.internalTrust,
    required this.externalTrust,
    required this.mentalStability,
    required this.socialLinks,
    required this.currentCard,
    required this.remainingCards,
  });

  @override
  List<Object?> get props => [
    internalTrust,
    externalTrust,
    mentalStability,
    socialLinks,
    currentCard,
    remainingCards,
  ];
}

class GameCardPreviewing extends GamePlaying {
  final bool isLeft;
  final Map<String, int> previewEffect;

  GameCardPreviewing({
    required super.internalTrust,
    required super.externalTrust,
    required super.mentalStability,
    required super.socialLinks,
    required super.currentCard,
    required super.remainingCards,
    required this.isLeft,
    required this.previewEffect,
  });


  @override
  List<Object?> get props => super.props + [isLeft];
}

class GameCardChosen extends GamePlaying {
  final bool isLeft;
  final Map<String, int> appliedEffect;

  GameCardChosen({
    required super.internalTrust,
    required super.externalTrust,
    required super.mentalStability,
    required super.socialLinks,
    required super.currentCard,
    required super.remainingCards,
    required this.isLeft,
    required this.appliedEffect,
  });

  @override
  List<Object?> get props => super.props + [isLeft, appliedEffect];
}

class GameOver extends GameState {
  final String reason;

  GameOver(this.reason);

}
