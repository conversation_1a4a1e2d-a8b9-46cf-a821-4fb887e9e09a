import 'package:flutter/material.dart';
import '../../data/models/card.dart' as game_card;

abstract class GameState {}

class GameInitial extends GameState {}

class GameLoading extends GameState {}

class GamePlaying extends GameState {
  // Game Progress State (được quản lý bởi Cubit)
  final int internalTrust;
  final int externalTrust;
  final int mentalStability;
  final int socialLinks;
  final game_card.Card currentCard;
  final List<game_card.Card> remainingCards;

  // Swipe Interaction State - moved from UI
  final Offset cardOffset;
  final double swipeOutRotation;
  final bool isCardAnimatingOut;
  final String swipeText;
  final bool shouldStartSwipeAnimation;
  final bool shouldStartResetAnimation;
  final Offset? swipeEndOffset; // For animation target

  GamePlaying({
    required this.currentCard,
    required this.remainingCards,
    required this.internalTrust,
    required this.externalTrust,
    required this.mentalStability,
    required this.socialLinks,
    this.cardOffset = Offset.zero,
    this.swipeOutRotation = 0.0,
    this.isCardAnimatingOut = false,
    this.swipeText = "",
    this.shouldStartSwipeAnimation = false,
    this.shouldStartResetAnimation = false,
    this.swipeEndOffset,
  });

  GamePlaying copyWith({
    game_card.Card? currentCard,
    List<game_card.Card>? remainingCards,
    int? internalTrust,
    int? externalTrust,
    int? mentalStability,
    int? socialLinks,
    Offset? cardOffset,
    double? swipeOutRotation,
    bool? isCardAnimatingOut,
    String? swipeText,
    bool? shouldStartSwipeAnimation,
    bool? shouldStartResetAnimation,
    Offset? swipeEndOffset,
  }) {
    return GamePlaying(
      currentCard: currentCard ?? this.currentCard,
      remainingCards: remainingCards ?? this.remainingCards,
      internalTrust: internalTrust ?? this.internalTrust,
      externalTrust: externalTrust ?? this.externalTrust,
      mentalStability: mentalStability ?? this.mentalStability,
      socialLinks: socialLinks ?? this.socialLinks,
      cardOffset: cardOffset ?? this.cardOffset,
      swipeOutRotation: swipeOutRotation ?? this.swipeOutRotation,
      isCardAnimatingOut: isCardAnimatingOut ?? this.isCardAnimatingOut,
      swipeText: swipeText ?? this.swipeText,
      shouldStartSwipeAnimation: shouldStartSwipeAnimation ?? this.shouldStartSwipeAnimation,
      shouldStartResetAnimation: shouldStartResetAnimation ?? this.shouldStartResetAnimation,
      swipeEndOffset: swipeEndOffset ?? this.swipeEndOffset,
    );
  }
}

class GameCardPreviewing extends GameState {
  final int internalTrust;
  final int externalTrust;
  final int mentalStability;
  final int socialLinks;
  final game_card.Card currentCard;
  final List<game_card.Card> remainingCards;
  final bool isLeft;
  final Map<String, int> previewEffect;
  final Offset cardOffset;
  final String swipeText;

  GameCardPreviewing({
    required this.internalTrust,
    required this.externalTrust,
    required this.mentalStability,
    required this.socialLinks,
    required this.currentCard,
    required this.remainingCards,
    required this.isLeft,
    required this.previewEffect,
    this.cardOffset = Offset.zero,
    this.swipeText = "",
  });

  GameCardPreviewing copyWith({
    game_card.Card? currentCard,
    List<game_card.Card>? remainingCards,
    int? internalTrust,
    int? externalTrust,
    int? mentalStability,
    int? socialLinks,
    bool? isLeft,
    Map<String, int>? previewEffect,
    Offset? cardOffset,
    String? swipeText,
  }) {
    return GameCardPreviewing(
      internalTrust: internalTrust ?? this.internalTrust,
      externalTrust: externalTrust ?? this.externalTrust,
      mentalStability: mentalStability ?? this.mentalStability,
      socialLinks: socialLinks ?? this.socialLinks,
      currentCard: currentCard ?? this.currentCard,
      remainingCards: remainingCards ?? this.remainingCards,
      isLeft: isLeft ?? this.isLeft,
      previewEffect: previewEffect ?? this.previewEffect,
      cardOffset: cardOffset ?? this.cardOffset,
      swipeText: swipeText ?? this.swipeText,
    );
  }
}

class GameOver extends GameState {
  final String message;

  GameOver(this.message);
}

class GameWin extends GameState {
  final String message;

  GameWin(this.message);
}
