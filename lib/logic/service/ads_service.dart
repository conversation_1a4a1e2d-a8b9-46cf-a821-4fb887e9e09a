import 'package:google_mobile_ads/google_mobile_ads.dart';

class AdsService {
  static final AdsService _instance = AdsService._internal();
  factory AdsService() => _instance;
  AdsService._internal();

  RewardedAd? _rewardedAd;
  bool _isLoading = false;

  Future<void> init() async {
    await MobileAds.instance.initialize();
    await loadRewardedAd();
  }
  
  Future<void> loadRewardedAd() async {
    if (_isLoading) return;
    _isLoading = true;

    try {
      await RewardedAd.load(
        adUnitId: 'ca-app-pub-5626821340108966/8577436934',
        request: const AdRequest(),
        rewardedAdLoadCallback: RewardedAdLoadCallback(
          onAdLoaded: (ad) {
            _rewardedAd = ad;
            _isLoading = false;
          },
          onAdFailedToLoad: (error) {
            _rewardedAd = null;
            _isLoading = false;
          },
        ),
      );
    } catch (e) {
      _rewardedAd = null;
      _isLoading = false;
    }
  }

  Future<void> showRewardedAd({
    required Function() onRewarded,
    required Function() onFailed,
  }) async {
    if (_rewardedAd == null) {
      onFailed(); // Gọi callback onFailed khi không có quảng cáo
      await loadRewardedAd(); // Tải quảng cáo mới cho lần sau
      return;
    }

    try {
      _rewardedAd!.fullScreenContentCallback = FullScreenContentCallback(
        onAdDismissedFullScreenContent: (ad) {
          ad.dispose();
          loadRewardedAd(); // Tải trước quảng cáo tiếp theo
        },
        onAdFailedToShowFullScreenContent: (ad, error) {
          ad.dispose();
          onFailed();
          loadRewardedAd(); // Tải quảng cáo mới
        },
      );

      await _rewardedAd!.show(
        onUserEarnedReward: (_, reward) => onRewarded(),
      );
    } catch (e) {
      onFailed();
      loadRewardedAd(); // Tải quảng cáo mới
    }
  }

  void dispose() {
    _rewardedAd?.dispose();
    _rewardedAd = null;
  }
}
