import 'dart:io';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:hive/hive.dart';

class AdsService {
  static final AdsService _instance = AdsService._internal();
  factory AdsService() => _instance;
  AdsService._internal();

  RewardedAd? _rewardedAd;
  bool _isLoadingRewarded = false;

  InterstitialAd? _interstitialAd;
  bool _isLoadingInterstitial = false;

  bool _isAdsRemoved = false;
  late Box _settingsBox;

  Future<void> setAdsRemoved(bool value) async {
    _isAdsRemoved = value;
    await _settingsBox.put('ads_removed', value);
  }

  bool get isAdsRemoved => _isAdsRemoved;

  Future<void> init() async {
    _settingsBox = await Hive.openBox('ads_settings');
    _isAdsRemoved = _settingsBox.get('ads_removed', defaultValue: false);

    await MobileAds.instance.initialize();
    loadRewardedAd();
    loadInterstitialAd();
  }

  Future<void> handleRemoveAdsPurchased() async {
    await setAdsRemoved(true);
  }

  // ---------------- Rewarded ----------------
  void loadRewardedAd() {
    if (_isLoadingRewarded) return;
    _isLoadingRewarded = true;
    final String adUnitId = Platform.isIOS
      ? 'ca-app-pub-5626821340108966/5895303392'
      : 'ca-app-pub-5626821340108966/8577436934';

    RewardedAd.load(
      adUnitId: adUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (ad) {
          _rewardedAd = ad;
          _isLoadingRewarded = false;
        },
        onAdFailedToLoad: (error) {
          _rewardedAd = null;
          _isLoadingRewarded = false;
        },
      ),
    );
  }

  Future<void> showRewardedAd({
    required Function() onRewarded,
    required Function() onFailed,
  }) async {
    if (_rewardedAd == null) {
      onFailed();
      loadRewardedAd();
      return;
    }

    _rewardedAd!.fullScreenContentCallback = FullScreenContentCallback(
      onAdDismissedFullScreenContent: (ad) {
        ad.dispose();
        _rewardedAd = null;
        loadRewardedAd();
      },
      onAdFailedToShowFullScreenContent: (ad, error) {
        ad.dispose();
        _rewardedAd = null;
        onFailed();
        loadRewardedAd();
      },
    );

    try {
      await _rewardedAd!.show(
        onUserEarnedReward: (_, reward) => onRewarded(),
      );
    } catch (e) {
      _rewardedAd?.dispose();
      _rewardedAd = null;
      onFailed();
      loadRewardedAd();
    }
  }

  // ---------------- Interstitial ----------------
  void loadInterstitialAd() {
    if (_isLoadingInterstitial) return;
    _isLoadingInterstitial = true;
    final String adUnitId = Platform.isIOS
      ? 'ca-app-pub-5626821340108966/6058285388'
      : 'ca-app-pub-5626821340108966/7185333092';

    InterstitialAd.load(
      adUnitId: adUnitId,
      request: const AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (ad) {
          _interstitialAd = ad;
          _isLoadingInterstitial = false;
        },
        onAdFailedToLoad: (error) {
          _interstitialAd = null;
          _isLoadingInterstitial = false;
        },
      ),
    );
  }

  Future<void> showInterstitialAd({
    required Function() onComplete,
    required Function() onFailed,
  }) async {
    if (_isAdsRemoved) {
      // Nếu đã mua remove ads, gọi onComplete ngay lập tức
      onComplete();
      return;
    }
    if (_interstitialAd == null) {
      onFailed();
      loadInterstitialAd(); // chỉ gọi, không await
      return;
    }

    _interstitialAd!.fullScreenContentCallback = FullScreenContentCallback(
      onAdDismissedFullScreenContent: (ad) {
        ad.dispose();
        _interstitialAd = null;
        onComplete();
        loadInterstitialAd();
      },
      onAdFailedToShowFullScreenContent: (ad, error) {
        ad.dispose();
        _interstitialAd = null;
        onComplete();
        loadInterstitialAd();
      },
    );

    try {
      _interstitialAd!.show();
    } catch (e) {
      _interstitialAd?.dispose();
      _interstitialAd = null;
      onComplete();
      loadInterstitialAd();
    }
  }

  // ---------------- Cleanup ----------------
  void dispose() {
    _rewardedAd?.dispose();
    _rewardedAd = null;
    _interstitialAd?.dispose();
    _interstitialAd = null;
  }
}
