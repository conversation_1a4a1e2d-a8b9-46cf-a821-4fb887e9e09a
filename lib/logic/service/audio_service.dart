import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';

enum SoundType {
  morse,
  buttonClick,
  swipeCard,
  notification,
  success,
  failure,
  background,
  playgame,
}

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  late Box settingsBox;
  bool isInitialized = false;

  AudioPlayer? _backgroundPlayer;
  AudioPlayer? _effectPlayer;

  DateTime? _lastSwipeSound;
  static const _minTimeBetweenSounds = Duration(milliseconds: 100);

  AudioPlayer? get backgroundPlayer => _backgroundPlayer;

  Future<void> init() async {
    try {
      if (isInitialized) return;

      await Hive.initFlutter();
      settingsBox = await Hive.openBox('audio_settings');

      if (!settingsBox.containsKey('isSoundEnabled')) {
        await settingsBox.put('isSoundEnabled', true);
      }
      if (!settingsBox.containsKey('isEffectEnabled')) {
        await settingsBox.put('isEffectEnabled', true);
      }

      _backgroundPlayer = AudioPlayer();
      _effectPlayer = AudioPlayer();

      isInitialized = true;
    } catch (e) {
      debugPrint('Error initializing AudioService: $e');
    }
  }

  Future<void> stopBackgroundMusic() async {
    try {
      await _backgroundPlayer?.stop();
    } catch (e) {
      debugPrint('Error stopping background music: $e');
    }
  }

  Future<void> stopAllSounds() async {
    try {
      await _backgroundPlayer?.stop();
      await _effectPlayer?.stop();
    } catch (e) {
      debugPrint('Error stopping sounds: $e');
    }
  }

  Future<void> playSwipeSound() async {
    if (!isInitialized) await init();
    if (!settingsBox.get('isEffectEnabled', defaultValue: true)) return;

    try {
      final now = DateTime.now();
      if (_lastSwipeSound != null && 
          now.difference(_lastSwipeSound!) < _minTimeBetweenSounds) {
        return;
      }
      _lastSwipeSound = now;

      // Tạo mới effect player cho swipe sound
      _effectPlayer?.dispose();
      _effectPlayer = AudioPlayer();
      
      await _effectPlayer?.setPlayerMode(PlayerMode.lowLatency);
      await _effectPlayer?.setVolume(0.8); // Tăng volume lên 0.8
      
      await _effectPlayer?.play(AssetSource('audio/swipeCard.mp4'));
      _effectPlayer?.setReleaseMode(ReleaseMode.release);
    } catch (e) {
      debugPrint('Error playing swipe sound: $e');
    }
  }

  Future<void> playSound(SoundType type) async {
    if (type == SoundType.swipeCard) {
      await playSwipeSound();
      return;
    }

    if (!isInitialized) await init();
    if (!settingsBox.get('isEffectEnabled', defaultValue: true)) return;

    try {
      _effectPlayer?.dispose();
      _effectPlayer = AudioPlayer();
      
      await _effectPlayer?.setPlayerMode(PlayerMode.lowLatency);
      await _effectPlayer?.setVolume(1.0);
      
      String soundFile;
      switch (type) {
        case SoundType.morse:
          soundFile = 'morse.mp4';
          break;
        case SoundType.buttonClick:
          soundFile = 'button_click.mp4';
          break;
        case SoundType.notification:
          soundFile = 'notification.mp4';
          break;
        case SoundType.success:
          soundFile = 'success.mp4';
          break;
        case SoundType.failure:
          soundFile = 'failure.mp4';
          break;
        case SoundType.background:
          soundFile = 'background.mp4';
          break;
        case SoundType.playgame:
          soundFile = 'playgame.mp4';
          break;
        default:
          soundFile = 'button_click.mp4';
      }
      
      await _effectPlayer?.play(AssetSource('audio/$soundFile'));
      _effectPlayer?.setReleaseMode(ReleaseMode.release);
    } catch (e) {
      debugPrint('Error playing sound effect: $e');
    }
  }

  Future<void> playLoop(String fileName) async {
    if (!isInitialized) await init();
    if (!settingsBox.get('isSoundEnabled', defaultValue: true)) return;

    try {
      _backgroundPlayer ??= AudioPlayer();
      await _backgroundPlayer?.stop();
      
      // Thiết lập AudioFocus cho background player
      await _backgroundPlayer?.setPlayerMode(PlayerMode.mediaPlayer);
      
      // Thiết lập sự kiện khi audio gần kết thúc
      _backgroundPlayer?.onPositionChanged.listen((Duration position) async {
        final duration = await _backgroundPlayer?.getDuration() ?? const Duration();
        // Khi còn 500ms trước khi kết thúc
        if (duration - position <= const Duration(milliseconds: 500)) {
          // Fade out mượt hơn với nhiều bước
          await _backgroundPlayer?.setVolume(0.9);
          await Future.delayed(const Duration(milliseconds: 100));
          await _backgroundPlayer?.setVolume(0.7);
          await Future.delayed(const Duration(milliseconds: 100));
          await _backgroundPlayer?.setVolume(0.5);
          await Future.delayed(const Duration(milliseconds: 100));
          await _backgroundPlayer?.setVolume(0.3);
          await Future.delayed(const Duration(milliseconds: 100));
          await _backgroundPlayer?.setVolume(0.1);
        }
        // Khi bắt đầu lại
        if (position <= const Duration(milliseconds: 500)) {
          // Fade in mượt hơn với nhiều bước
          await _backgroundPlayer?.setVolume(0.1);
          await Future.delayed(const Duration(milliseconds: 100));
          await _backgroundPlayer?.setVolume(0.3);
          await Future.delayed(const Duration(milliseconds: 100));
          await _backgroundPlayer?.setVolume(0.5);
          await Future.delayed(const Duration(milliseconds: 100));
          await _backgroundPlayer?.setVolume(0.7);
          await Future.delayed(const Duration(milliseconds: 100));
          await _backgroundPlayer?.setVolume(0.9);
          await Future.delayed(const Duration(milliseconds: 100));
          await _backgroundPlayer?.setVolume(1.0);
        }
      });

      await _backgroundPlayer?.play(AssetSource('audio/$fileName'));
      await _backgroundPlayer?.setVolume(1.0);
      _backgroundPlayer?.setReleaseMode(ReleaseMode.loop);
    } catch (e) {
      debugPrint('Error playing loop audio: $e');
    }
  }

  // Chỉ gọi dispose khi thực sự cần thiết (ví dụ: khi app đóng hoàn toàn)
  Future<void> dispose() async {
    if (!isInitialized) return;

    await stopAllSounds();
    await _backgroundPlayer?.dispose();
    await _effectPlayer?.dispose();

    _backgroundPlayer = null;
    _effectPlayer = null;

    isInitialized = false;
  }
}








