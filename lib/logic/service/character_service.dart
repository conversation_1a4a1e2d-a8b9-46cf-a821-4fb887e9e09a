import '../../data/repositories/character_repository.dart';
import '../../data/database/database_helper.dart';
import '../../data/models/character.dart';

class CharacterService {
  late final CharacterRepository _characterRepository;

  CharacterService() {
    _characterRepository = CharacterRepository(DatabaseHelper.instance);
  }

  Future<void> init() async {
    await DatabaseHelper.instance.initializeDatabase();
  }

  // Lấy tổng số nhân vật
  Future<int> getTotalCharacterCount() async {
    return await _characterRepository.getCharacterCount();
  }

  // Lấy số nhân vật đã mở khóa
  Future<int> getUnlockedCharacterCount() async {
    final unlockedCharacters = await _characterRepository.getUnlockedCharacters();
    return unlockedCharacters.length;
  }

  // Lấy tỷ lệ mở khóa (0.0 - 1.0)
  Future<double> getUnlockProgress() async {
    final total = await getTotalCharacterCount();
    if (total == 0) return 0.0;

    final unlocked = await getUnlockedCharacterCount();
    return unlocked / total;
  }

  // Lấy tất cả nhân vật
  Future<List<Character>> getAllCharacters() async {
    return await _characterRepository.getAllCharacters();
  }

  // Lấy nhân vật đã mở khóa
  Future<List<Character>> getUnlockedCharacters() async {
    return await _characterRepository.getUnlockedCharacters();
  }

  // Mở khóa nhân vật theo id
  Future<void> unlockCharacter(int characterId) async {
    await _characterRepository.unlockCharacter(characterId);
  }
}
