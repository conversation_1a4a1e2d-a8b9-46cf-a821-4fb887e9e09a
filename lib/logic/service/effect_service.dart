import 'package:hive/hive.dart';
import 'package:spy/data/models/effect_card.dart';
import 'package:spy/logic/service/score_service.dart';

class EffectService {
  static const String _boxName = 'effects';
  late final Box _box;
  final ScoreService _scoreService;

  EffectService(this._scoreService);

  Future<void> init() async {
    _box = await Hive.openBox(_boxName);
  }

  int getEffectQuantity(String effectTitle) {
    return _box.get(effectTitle, defaultValue: 0);
  }

  Future<bool> buyEffect(EffectCard effect) async {
    final success = await _scoreService.decreaseCoin(effect.price);
    if (success) {
      final currentQuantity = getEffectQuantity(effect.title);
      await _box.put(effect.title, currentQuantity + 1);
      return true;
    }
    return false;
  }

  Future<void> useEffect(String effectTitle) async {
    final currentQuantity = getEffectQuantity(effectTitle);
    if (currentQuantity > 0) {
      await _box.put(effectTitle, currentQuantity - 1);
    }
  }

  // Check if player has item for specific death type and use it
  Future<bool> tryUseItemForDeath(String deathType) async {
    String itemTitle;
    switch (deathType) {
      case 'internal':
        itemTitle = 'Eye';
        break;
      case 'external':
        itemTitle = 'Military badge';
        break;
      case 'mental':
        itemTitle = 'Heart';
        break;
      case 'social':
        itemTitle = 'Newspaper';
        break;
      default:
        return false;
    }

    final quantity = getEffectQuantity(itemTitle);
    if (quantity > 0) {
      await useEffect(itemTitle);
      return true;
    }
    return false;
  }
}
