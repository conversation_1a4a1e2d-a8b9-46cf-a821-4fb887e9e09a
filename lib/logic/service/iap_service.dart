import 'package:in_app_purchase/in_app_purchase.dart';
import 'dart:async';

class IAPService {
  static final IAPService instance = IAPService._internal();
  factory IAPService() => instance;
  IAPService._internal();

  final InAppPurchase _iap = InAppPurchase.instance;
  final String removeAdsProductId = 'remove_ads';

  // ---------------- Init ----------------
  Future<void> init() async {
    // Listen purchase updates
  }

  // ---------------- Buy ----------------
  Future<void> buyRemoveAds() async {
    final ProductDetailsResponse response =
        await _iap.queryProductDetails({removeAdsProductId});

    if (response.notFoundIDs.isNotEmpty || response.productDetails.isEmpty) {
      throw Exception('Product not found');
    }

    final ProductDetails productDetails = response.productDetails.first;
    final PurchaseParam purchaseParam =
        PurchaseParam(productDetails: productDetails);

    await _iap.buyNonConsumable(purchaseParam: purchaseParam);
  }

  // ---------------- Restore ----------------
  Future<void> restorePurchases() async {
    await _iap.restorePurchases();
  }

  // ---------------- Utility ----------------
  Future<bool> isAvailable() async {
    return await _iap.isAvailable();
  }

  Future<String?> hasLoadedProduct() async {
    final ProductDetailsResponse response =
        await _iap.queryProductDetails({removeAdsProductId});

    if (response.error != null) {
      print("❌ Lỗi khi query: ${response.error}");
      return null;
    }

    if (response.notFoundIDs.isNotEmpty) {
      print("⚠️ Không tìm thấy sản phẩm: ${response.notFoundIDs}");
      return null;
    }

    if (response.productDetails.isNotEmpty) {
      final productId = response.productDetails.first.id;
      print("✅ Đã load sản phẩm: $productId");
      return productId;
    }

    return null;
  }

  Future<String?> getProductPriceString(String productId) async {
    final response = await _iap.queryProductDetails({productId});

    if (response.productDetails.isNotEmpty) {
      final product = response.productDetails.first;
      return product.price;
    }

    return null;
  }
}
