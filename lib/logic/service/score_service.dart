import 'package:hive/hive.dart';

class ScoreService {
  static const String _boxName = 'score';
  static const String _highScoreKey = 'highScore';
  static const String _scoreKey = 'score';
  static const String _coinKey = 'coin';

  late final Box _box;

  Future<void> init() async {
    _box = await Hive.openBox(_boxName);
     await _box.put(_coinKey, 99999);
  }

  int get score => _box.get(_scoreKey, defaultValue: 0);
  int get coin => _box.get(_coinKey, defaultValue: 0);
  int get highScore => _box.get(_highScoreKey, defaultValue: 0);

  Future<void> increaseScore(int amount) async {
    int newScore = score + amount;
    await _box.put(_scoreKey, newScore);
  }

  Future<void> resetScore() async {
    await _box.put(_scoreKey, 0);
  }

  Future<bool> decreaseCoin(int amount) async {
    if (coin >= amount) {
      await _box.put(_coinKey, coin - amount);
      return true;
    }
    return false;
  }

  Future<void> increaseCoin(int amount) async {
    await _box.put(_coinKey, coin + amount);
  }

  Future<void> updateHighScore() async {
    if (score > highScore) {
      await _box.put(_highScoreKey, score);
    }
  }

  Future<void> handleGameOver() async {
    int coin = await getCoin();
    int highScore = await getHighScore();

    await updateHighScore(); // Cập nhật highScore nếu cần

    await increaseCoin((score / 10).floor());
    await resetScore();
  }

  Future<int> getScore() async {
    return _box.get(_scoreKey, defaultValue: 0);
  }

  Future<int> getCoin() async {
    return _box.get(_coinKey, defaultValue: 0);
  }

  Future<int> getHighScore() async {
    return _box.get(_highScoreKey, defaultValue: 0);
  }
}
