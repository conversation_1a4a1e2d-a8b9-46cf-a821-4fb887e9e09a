import '../../data/repositories/story_repository.dart';
import '../../data/database/database_helper.dart';
import '../../data/models/story_line.dart';

class StoryService {
  late final StoryRepository _storyRepository;

  StoryService() {
    _storyRepository = StoryRepository.instance;
  }

  Future<void> init() async {
    await DatabaseHelper.instance.initializeDatabase();
  }

  // Lấy tổng số story line
  Future<int> getTotalStoryCount() async {
    return await _storyRepository.getTotalCount();
  }

  // Lấy số story line đã mở khóa
  Future<int> getUnlockedStoryCount() async {
    return await _storyRepository.getUnlockedCount();
  }

  // Lấy tỷ lệ mở khóa (0.0 - 1.0)
  Future<double> getUnlockProgress() async {
    final total = await getTotalStoryCount();
    if (total == 0) return 0.0;

    final unlocked = await getUnlockedStoryCount();
    return unlocked / total;
  }

  // Lấy tất cả story line
  Future<List<StoryLine>> getAllStoryLines() async {
    return await _storyRepository.getAllStoryLines();
  }

  // Lấy story line đã mở khóa
  Future<List<StoryLine>> getUnlockedStoryLines() async {
    return await _storyRepository.getUnlockedStoryLines();
  }

  // Lấy story line theo ID
  Future<StoryLine?> getStoryLineById(int id) async {
    return await _storyRepository.getStoryLineById(id);
  }

  // Cập nhật trạng thái mở khóa cho story line
  Future<bool> updateStoryUnlockStatus(int id, bool isUnlocked) async {
    final result = await _storyRepository.updateUnlockStatus(id, isUnlocked);
    return result > 0;
  }

  // Mở khóa story line
  Future<bool> unlockStory(int id) async {
    return await updateStoryUnlockStatus(id, true);
  }

  // Khóa story line
  Future<bool> lockStory(int id) async {
    return await updateStoryUnlockStatus(id, false);
  }
}
