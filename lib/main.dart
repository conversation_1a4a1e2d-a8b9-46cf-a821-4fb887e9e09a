import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:spy/presentation/game/intro_screen.dart';
import 'package:spy/data/database/database_helper.dart';
import 'logic/service/score_service.dart';
import 'logic/service/ads_service.dart';
import 'logic/service/iap_service.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  await Hive.initFlutter();
  final scoreService = ScoreService();
  await scoreService.init();
  
  final adsService = AdsService();
  await adsService.init();

  final iapService = IAPService.instance;
  await iapService.init();

  // Initialize the database and insert mock data
  await DatabaseHelper.instance.initializeDatabase();

  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // App resumed - check if account changed
      _checkAccountChanges();
    }
  }

  Future<void> _checkAccountChanges() async {
    // Force check current account's purchase status when app resumes
    try {
      await IAPService.instance.checkCurrentAccountPurchases();
    } catch (e) {
      // Ignore errors during background check
    }
  }

  @override
  Widget build(BuildContext context) {
    const primaryDark = Color(0xFF08121e);
    const primaryMid = Color(0xFF2c3542);

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Perfect Spy',
      theme: ThemeData(
        primaryColor: primaryMid,
        scaffoldBackgroundColor: primaryDark,
        canvasColor: primaryDark,
        appBarTheme: const AppBarTheme(
          backgroundColor: primaryDark,
          elevation: 0,
        ),
        textTheme: GoogleFonts.gelasioTextTheme(),
        fontFamily: GoogleFonts.gelasio().fontFamily,
        colorScheme: const ColorScheme.dark(
          primary: primaryMid,
          surface: primaryDark,
          onSurface: Colors.white,
        ),
      ),
      home: const IntroScreen(),
    );
  }
}
