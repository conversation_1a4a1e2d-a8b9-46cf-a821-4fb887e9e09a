import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import '../../../logic/service/audio_service.dart';
import '../../../logic/service/ads_service.dart';
import '../../../logic/service/score_service.dart';

class WatchAdsButton extends StatefulWidget {
  final VoidCallback? onCoinsUpdated; // Thêm callback

  const WatchAdsButton({
    super.key,
    this.onCoinsUpdated,
  });

  @override
  State<WatchAdsButton> createState() => _WatchAdsButtonState();
}

class _WatchAdsButtonState extends State<WatchAdsButton> {
  final AudioService _audioService = AudioService();
  final AdsService _adsService = AdsService();
  final ScoreService _scoreService = ScoreService();

  @override
  void initState() {
    super.initState();
    _initServices();
  }

  Future<void> _initServices() async {
    await _scoreService.init();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 2),
      ),
      child: Center(
        child: IconButton(
          icon: const Icon(Icons.add, color: Colors.white),
          iconSize: 16,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          onPressed: () {
            _audioService.playSound(SoundType.buttonClick);
            _showWatchAdsDialog(context);
          },
        ),
      ),
    );
  }

  void _showWatchAdsDialog(BuildContext context) {
    String? currentMusic;
    if (_audioService.backgroundPlayer?.state == PlayerState.playing) {
      final source = _audioService.backgroundPlayer?.source as AssetSource?;
      if (source != null) {
        currentMusic = source.path.split('/').last;
      }
    }

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Theme.of(context).primaryColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5),
          side: const BorderSide(color: Colors.white),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.ondemand_video,
                color: Colors.white,
                size: 40,
              ),
              const SizedBox(height: 16),
              const Text(
                'Watch ads to earn 10 gold',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Wrap(
                spacing: 16,
                alignment: WrapAlignment.center,
                children: [
                  InkWell(
                    onTap: () async {
                      _audioService.playSound(SoundType.buttonClick);
                      Navigator.of(context).pop();
                      
                      await _audioService.stopBackgroundMusic();
                      
                      await _adsService.showRewardedAd(
                        onRewarded: () async {
                          await _scoreService.increaseCoin(10);
                          
                          // Gọi callback để update UI của EffectsScreen
                          widget.onCoinsUpdated?.call();
                          
                          if (currentMusic != null) {
                            await _audioService.playLoop(currentMusic);
                          }
                          
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Bạn nhận được 10 gold!'),
                                duration: Duration(seconds: 2),
                              ),
                            );
                          }
                        },
                        onFailed: () async {
                          if (currentMusic != null) {
                            await _audioService.playLoop(currentMusic);
                          }
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Không thể tải quảng cáo. Vui lòng thử lại sau!'),
                                duration: Duration(seconds: 2),
                              ),
                            );
                          }
                        },
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 24,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.white),
                        borderRadius: BorderRadius.circular(5),
                      ),
                      child: const Text(
                        'Xem',
                        style: TextStyle(
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                  // Thêm nút Cancel
                  InkWell(
                    onTap: () {
                      _audioService.playSound(SoundType.buttonClick);
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 24,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.white),
                        borderRadius: BorderRadius.circular(5),
                      ),
                      child: const Text(
                        'Hủy',
                        style: TextStyle(
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
