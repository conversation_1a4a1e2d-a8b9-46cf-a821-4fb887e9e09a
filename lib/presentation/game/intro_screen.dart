import 'package:flutter/material.dart';
import 'package:spy/presentation/game/main_menu_screen.dart';
import 'package:spy/logic/service/audio_service.dart';

class IntroScreen extends StatefulWidget {
  const IntroScreen({super.key});

  @override
  State<IntroScreen> createState() => _IntroScreenState();
}

class _IntroScreenState extends State<IntroScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  Animation<double>? _fadeAnimation; // Đổi thành nullable
  final AudioService _audioService = AudioService();
  bool _isInitialized = false; // Thêm flag kiểm tra khởi tạo

  @override
  void initState() {
    super.initState();
    _initializeAudioAndAnimation();
  }

  Future<void> _initializeAudioAndAnimation() async {
    await _audioService.init();
    await _audioService.playSound(SoundType.morse); // Thêm âm thanh intro
    
    _controller = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.5, curve: Curves.easeIn),
      ),
    );

    setState(() {
      _isInitialized = true;
    });

    _controller.forward();

    await Future.delayed(const Duration(seconds: 1));
    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              const MainMenuScreen(),
          transitionsBuilder:
              (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 500),
        ),
      );
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    // Bỏ _audioService.dispose() ở đây vì sẽ cần dùng tiếp ở main menu
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Hiển thị loading khi chưa khởi tạo xong
    if (!_isInitialized) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      body: Center(
        child: FadeTransition(
          opacity: _fadeAnimation!,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                'assets/images/logo_spy.webp',
                width: MediaQuery.of(context).size.width * 0.4,
                height: MediaQuery.of(context).size.height * 0.4,
                fit: BoxFit.contain,
                filterQuality: FilterQuality.high,
                gaplessPlayback: true,
                isAntiAlias: true,
              ),
              SizedBox(height: MediaQuery.of(context).size.height * 0.05),
              Text(
                'PERFECT SPY',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 30,
                  fontWeight: FontWeight.w900,
                  letterSpacing: 4,
                  height: 1,
                  shadows: [
                    Shadow(
                      color: Colors.black.withValues(alpha: 0.6),
                      offset: const Offset(3, 3),
                      blurRadius: 5,
                    ),
                    Shadow(
                      color: Theme.of(context).primaryColor.withValues(alpha: 0.4),
                      offset: const Offset(-2, -2),
                      blurRadius: 4,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
