import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:spy/logic/service/score_service.dart';
import 'package:spy/presentation/game/main_screen.dart';
import 'package:spy/presentation/settings/settings_screen.dart';

import '../../data/repositories/card_repository.dart';
import '../../logic/cubit/game_cubit.dart';
import '../../logic/service/audio_service.dart';
import '../../logic/service/story_service.dart';
import '../../logic/service/character_service.dart';

class MainMenuScreen extends StatefulWidget {
  const MainMenuScreen({super.key});

  @override
  State<MainMenuScreen> createState() => _MainMenuScreenState();
}

class _MainMenuScreenState extends State<MainMenuScreen>
    with WidgetsBindingObserver {
  final AudioService _audioService = AudioService();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initBackgroundMusic();
  }

  Future<void> _initBackgroundMusic() async {
    await _audioService.init();
    await _audioService.playLoop('background.mp4');
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _audioService.playLoop('background.mp4');
    } else if (state == AppLifecycleState.paused) {
      _audioService.stopBackgroundMusic();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    // Không dispose audio service ở đây
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          _audioService.playSound(SoundType.buttonClick);
          _audioService.stopBackgroundMusic();
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => BlocProvider(
                create: (_) => GameCubit(
                  CardRepository(),
                  ScoreService(),
                  StoryService(),
                  CharacterService(),
                ),
                child: const MainScreen(),
              ),
            ),
          );
        },
        child: Stack(
          children: [
            // Nội dung chính
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/images/logo_spy.webp',
                    width: MediaQuery.of(context).size.width * 0.4,
                    height: MediaQuery.of(context).size.width * 0.4,
                    fit: BoxFit.contain,
                  ),
                  SizedBox(height: MediaQuery.of(context).size.height * 0.05),
                  Text(
                    'PERFECT SPY',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 28,
                      fontWeight: FontWeight.w900,
                      letterSpacing: 4,
                      height: 1,
                      shadows: [
                        Shadow(
                          color: Colors.black.withValues(alpha: 0.6),
                          offset: const Offset(3, 3),
                          blurRadius: 5,
                        ),
                        Shadow(
                          color: Theme.of(context).primaryColor.withValues(alpha: 0.4),
                          offset: const Offset(-2, -2),
                          blurRadius: 4,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 100),
                  // Button chỉ để hiển thị
                  const CustomButton(),
                ],
              ),
            ),

            // Icon settings - GestureDetector riêng
            Positioned(
              top: 40,
              left: 0,
              right: 0,
              child: Center(
                child: GestureDetector(
                  onTap: () {
                    _audioService.playSound(SoundType.buttonClick);
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (_) => const SettingsScreen(),
                      ),
                    );
                  },
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    child: const Icon(
                      Icons.settings,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomButton extends StatelessWidget {
  const CustomButton({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;

    return Stack(
      alignment: Alignment.center,
      children: [
        // Container nền chạy hết màn hình
        Container(
          width: screenWidth,
          height: 100,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary,
            border: Border(
              top: BorderSide(
                color: Colors.white.withValues(alpha: 0.5),
                width: 2,
              ),
              bottom: BorderSide(
                color: Colors.white.withValues(alpha: 0.5),
                width: 2,
              ),
            ),
          ),
        ),
        // Chữ TIẾP TỤC (không còn bấm riêng)
        const Text(
          'TIẾP TỤC',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w500,
            letterSpacing: 1.5,
          ),
        ),
      ],
    );
  }
}
