import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:spy/presentation/game/widget/progress_icon.dart';
import '../../logic/cubit/game_cubit.dart';
import '../../logic/cubit/game_state.dart';
import '../../logic/service/audio_service.dart';
import '../main_menu_screen.dart';
import '../settings/settings_screen.dart';
import '../../logic/service/effect_service.dart';
import '../../logic/service/score_service.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with TickerProviderStateMixin {
  final AudioService _audioService = AudioService();
  final EffectService _effectService = EffectService(ScoreService());
  bool _effectsInitialized = false;

  bool isCardAnimatingOut = false;
  late AnimationController _resetController;
  late Animation<Offset> _resetAnimation;

  late AnimationController _swipeOutController;
  late Animation<Offset> _swipeOutAnimation;

  late AnimationController _showCardController;
  late Animation<double> _fadeInAnimation;
  late Animation<double> _scaleInAnimation;

  double swipeOutRotation = 0;
  bool _isGameOver = false;

  Offset cardOffset = Offset.zero;
  final double swipeThreshold = 120;
  String swipeText = "";
  String? _currentCardId; // Track the current card's ID

  late AnimationController _fadeOutController;
  late Animation<double> _fadeOutAnimation;

  @override
  void initState() {
    super.initState();
    context.read<GameCubit>().startGame();
    _initGameMusic(); // Thêm init music

    _resetController =
        AnimationController(
            vsync: this,
            duration: const Duration(milliseconds: 200),
          )
          ..addListener(() {
            setState(() {
              cardOffset = _resetAnimation.value;
            });
          })
          ..addStatusListener((status) {
            if (status == AnimationStatus.completed) {
              swipeText = "";
            }
          });

    _swipeOutController =
        AnimationController(
            vsync: this,
            duration: const Duration(
              milliseconds: 500,
            ), // Tăng lên 800ms để trượt chậm hơn
          )
          ..addListener(() {
            setState(() {
              cardOffset = _swipeOutAnimation.value;
            });
          })
          ..addStatusListener((status) {
            if (status == AnimationStatus.completed) {
              context.read<GameCubit>().chooseCard(isLeft: cardOffset.dx < 0);
              setState(() {
                cardOffset = Offset.zero;
                swipeText = "";
                swipeOutRotation = 0; // Reset góc xoay ngay sau khi trượt xong
                isCardAnimatingOut = false;
              });
            }
          });

    _showCardController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _fadeInAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _showCardController, curve: Curves.easeOut),
    );

    _scaleInAnimation = Tween<double>(begin: 1.0, end: 1.0).animate(
      CurvedAnimation(parent: _showCardController, curve: Curves.easeOut),
    );

    _fadeOutController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _fadeOutAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _fadeOutController, curve: Curves.easeOut),
    );
  }

  Future<void> _initGameMusic() async {
    await _audioService.init();
    await _audioService.playLoop('playgame.mp4');
    await _effectService.init();
    setState(() {
      _effectsInitialized = true;
    });
  }

  @override
  void dispose() {
    _resetController.dispose();
    _swipeOutController.dispose();
    _showCardController.dispose();
    _fadeOutController.dispose();
    super.dispose();
  }

  void _onPanUpdate(DragUpdateDetails details) {
    final card = (context.read<GameCubit>().state as GamePlaying).currentCard;

    setState(() {
      cardOffset += details.delta;

      if (cardOffset.dx > 20) {
        swipeText = card.rightText;
        context.read<GameCubit>().previewCard(isLeft: false);
      } else if (cardOffset.dx < -20) {
        swipeText = card.leftText;
        context.read<GameCubit>().previewCard(isLeft: true);
      } else {
        swipeText = "";
      }
    });
  }

  void _onPanEnd(DragEndDetails details) {
    final cubit = context.read<GameCubit>();

    final isSwipeRight = cardOffset.dx > swipeThreshold;
    final isSwipeLeft = cardOffset.dx < -swipeThreshold;

    if (isSwipeRight || isSwipeLeft) {
      // Gọi trực tiếp phương thức playSwipeSound
      _audioService.playSwipeSound();
      
      setState(() {
        isCardAnimatingOut = true;
        swipeOutRotation = cardOffset.dx * 0.002;
      });

      final screenWidth = MediaQuery.of(context).size.width;
      final endOffset = Offset(
        isSwipeRight ? screenWidth * 1.5 : -screenWidth * 1.5,
        cardOffset.dy,
      );

      _swipeOutAnimation = Tween<Offset>(
        begin: cardOffset,
        end: endOffset,
      ).animate(
        CurvedAnimation(parent: _swipeOutController, curve: Curves.easeOut),
      );

      _swipeOutController.forward(from: 0.0);
    } else {
      cubit.cancelPreview();

      _resetAnimation = Tween<Offset>(
        begin: cardOffset,
        end: Offset.zero,
      ).animate(
        CurvedAnimation(parent: _resetController, curve: Curves.easeOut),
      );

      _resetController.forward(from: 0.0);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textScaler = MediaQuery.of(context).textScaler;
    // ignore: deprecated_member_use
    const baseSize = 17.0;

    const double por1 = 0.13;
    const double  por2 = 0.67;
    const double por3 = 0.12;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: FadeTransition(
        opacity: _fadeOutAnimation,
        child: SafeArea(
          child: BlocBuilder<GameCubit, GameState>(
            builder: (context, state) {
              if (state is GameInitial) {
                return const Center(child: CircularProgressIndicator());
              }

              if (state is GameOver) {
                if (!_isGameOver) {
                  _isGameOver = true;
                  _fadeOutController.forward().then((_) {
                    Navigator.of(context).pushReplacement(
                      PageRouteBuilder(
                        pageBuilder:
                            (context, animation, secondaryAnimation) =>
                                const MainMenuScreen(),
                        transitionsBuilder: (
                          context,
                          animation,
                          secondaryAnimation,
                          child,
                        ) {
                          return FadeTransition(
                            opacity: animation,
                            child: child,
                          );
                        },
                        transitionDuration: const Duration(seconds: 2),
                      ),
                    );
                  });
                }
              }

              if (state is GamePlaying || state is GameCardPreviewing) {
                final s = state as GamePlaying;
                final card = s.currentCard;

                final isCritical =
                    s.internalTrust <= 0 ||
                    s.externalTrust <= 0 ||
                    s.mentalStability <= 0 ||
                    s.socialLinks <= 0;

                // Check if the card has changed
                if (_currentCardId != card.id) {
                  _currentCardId = card.id;
                  _showCardController.forward(from: 0.0);
                }

                final isPreviewing = state is GameCardPreviewing;
                final showLeft = isPreviewing && cardOffset.dx < -20;
                final showRight = isPreviewing && cardOffset.dx > 20;

                // Parse the effect strings into maps with explicit type
                Map<String, int> previewEffects = <String, int>{};
                if (showLeft) {
                  previewEffects = card.leftEffectMap;
                } else if (showRight) {
                  previewEffects = card.rightEffectMap;
                }

                return Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // Item and indicator
                    Container(
                      color: theme.scaffoldBackgroundColor,
                      height: MediaQuery.of(context).size.height * por1,
                      child: Center(
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: MediaQuery.of(context).size.width * 0.1),
                          child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  ProgressIcon(
                                    progress: s.internalTrust / 10,
                                    icon: Icons.remove_red_eye,
                                    fillColor: Colors.white,
                                    showDot:
                                        previewEffects.containsKey('internalTrust') &&
                                            (previewEffects['internalTrust']!).abs() > 0,
                                    bigDot: previewEffects.containsKey('internalTrust') &&
                                        (previewEffects['internalTrust']!).abs() >= 3,
                                  ),
                                  ProgressIcon(
                                    progress: s.externalTrust / 10,
                                    icon: Icons.gps_fixed,
                                    fillColor: Colors.white,
                                    showDot:
                                        previewEffects.containsKey('externalTrust') &&
                                            (previewEffects['externalTrust']!).abs() > 0,
                                    bigDot: previewEffects.containsKey('externalTrust') &&
                                        (previewEffects['externalTrust']!).abs() >= 3,
                                  ),
                                  ProgressIcon(
                                    progress: s.mentalStability / 10,
                                    icon: Icons.heart_broken,
                                    fillColor: Colors.white,
                                    showDot:
                                        previewEffects.containsKey('mentalStability') &&
                                            (previewEffects['mentalStability']!).abs() > 0,
                                    bigDot: previewEffects.containsKey('mentalStability') &&
                                        (previewEffects['mentalStability']!).abs() >= 3,
                                  ),
                                  ProgressIcon(
                                    progress: s.socialLinks / 10,
                                    icon: Icons.groups_3,
                                    fillColor: Colors.white,
                                    showDot:
                                        previewEffects.containsKey('socialLinks') &&
                                            (previewEffects['socialLinks']!).abs() > 0,
                                    bigDot: previewEffects.containsKey('socialLinks') &&
                                        (previewEffects['socialLinks']!).abs() >= 3,
                                  ),
                                ],
                              ),
                              Padding(
                                padding: const EdgeInsets.only(top: 0, bottom: 0),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Container(
                                      width: MediaQuery.of(context).size.width * 0.42, // 30% chiều rộng màn hình
                                      height: 25, // cao 5
                                      decoration: BoxDecoration(
                                        color: Colors.transparent, // hoặc bạn đặt màu nền nếu muốn
                                        border: Border.all(color: Colors.white), // viền trắng
                                        borderRadius: BorderRadius.circular(20), // bo góc 20
                                      ),
                                      child: _effectsInitialized ? Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          for (var effect in [
                                            {'title': 'Eye', 'icon': 'assets/icons/eye.webp'},
                                            {'title': 'Military badge', 'icon': 'assets/icons/star.webp'},
                                            {'title': 'Heart', 'icon': 'assets/icons/heart.webp'},
                                            {'title': 'Newspaper', 'icon': 'assets/icons/newspaper.webp'},
                                          ])
                                            if (_effectService.getEffectQuantity(effect['title']!) > 0)
                                              Padding(
                                                padding: const EdgeInsets.symmetric(horizontal: 4),
                                                child: Image.asset(
                                                  effect['icon']!,
                                                  width: 15,
                                                  height: 15,
                                                ),
                                              ),
                                        ],
                                      ) : null,
                                    ),
                                  ],
                                ),
                              )

                            ],
                          ),
                        ),
                      ),
                    ),
                    // Card area
                    AnimatedContainer(
                      duration: const Duration(seconds: 3),
                      color:
                          isCritical
                              ? theme.scaffoldBackgroundColor
                              : const Color(0xFF9c9389),
                      width: double.infinity,
                      height: MediaQuery.of(context).size.height * por2,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            child: SizedBox(
                              height:
                                  MediaQuery.of(context).size.height * 0.23 * por2,
                              child: Padding(
                                padding: const EdgeInsets.only(top: 5, bottom: 5),
                                child: Center(
                                  child: SingleChildScrollView(
                                    child: DefaultTextStyle.merge(
                                      style: TextStyle(
                                        color:
                                            isCritical
                                                ? Colors.white
                                                : theme
                                                    .scaffoldBackgroundColor,
                                      ),
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            card.description,
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              fontWeight: FontWeight.normal,
                                              fontSize: textScaler.scale(baseSize),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          //const SizedBox(height: 10),
                          // Card swipe area
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              color: Colors.white30,
                            ),
                            child: GestureDetector(
                              onPanUpdate: _onPanUpdate,
                              onPanEnd: _onPanEnd,
                              child:
                                  isCardAnimatingOut
                                      ? Transform.translate(
                                        offset: cardOffset,
                                        child: Transform.rotate(
                                          angle: swipeOutRotation,
                                          child: _buildCard(card),
                                        ),
                                      )
                                      : _buildCard(card),
                            ),
                          ),
                          SizedBox(
                            height: MediaQuery.of(context).size.height * 0.08 * por2,
                            child: Center(
                              // Center toàn bộ SingleChildScrollView
                              child: Padding(
                                padding: const EdgeInsets.only(top: 5, bottom: 0),
                                child: SingleChildScrollView(
                                  scrollDirection: Axis.vertical,
                                  // Cuộn theo chiều DỌC
                                  child: Padding(
                                    padding: const EdgeInsets.only(
                                      left: 15,
                                      right: 15,
                                    ),
                                    child: Text(
                                      card.character != null
                                          ? card.character!.name
                                          : 'Không có nhân vật',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: isCritical
                                            ? Colors.white
                                            : theme.scaffoldBackgroundColor,
                                        fontSize: textScaler.scale(baseSize),
                                        fontStyle: FontStyle.normal,
                                      ),
                                    ),

                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Swipe text and settings
                    Container(
                      height: MediaQuery.of(context).size.height * por3,
                      alignment: Alignment.center,
                      child: GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          _audioService.playSound(SoundType.buttonClick); // Thêm âm thanh
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => const SettingsScreen(),
                            ),
                          );
                        },
                        child: AnimatedOpacity(
                          duration: const Duration(milliseconds: 100),
                          opacity: swipeText.isNotEmpty ? 1.0 : 0.0,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Center(
                              child: Text(
                                swipeText,
                                textAlign: TextAlign.center,
                                softWrap: true,
                                overflow: TextOverflow.visible,
                                style: TextStyle(
                                  fontSize: textScaler.scale(baseSize),
                                  color: Colors.white70,
                                  fontWeight: FontWeight.normal,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              }

              // if(state is NotificationState){
              //   print("using effect");
              //   NotificationService.showMessage(context, "state.message");
              // }
        

              return const Center(
                child: Text("", style: TextStyle(color: Colors.white)),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildCard(card) {
    final containerHeight = MediaQuery.of(context).size.height * 0.67; // lấy height AnimatedContainer
    final cardHeight = containerHeight * 0.67; // Thẻ chiếm 60% AnimatedContainer
    final cardWidth = cardHeight * (1.1 / 1.25);

    return FadeTransition(
      opacity: _fadeInAnimation,
      child: ScaleTransition(
        scale: _scaleInAnimation,
        child: Transform.translate(
          offset: cardOffset,
          child: Transform.rotate(
            angle: isCardAnimatingOut ? swipeOutRotation : cardOffset.dx * 0.002,
            child: Container(
              width: cardWidth,
              height: cardHeight,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                color: Colors.white70,
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child: card.character != null && card.character!.imgUrl.isNotEmpty
                  ? Image.asset(
                      card.character!.imgUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[300],
                          child: const Icon(
                            Icons.person,
                            color: Colors.grey,
                            size: 50,
                          ),
                        );
                      },
                    )
                  : Container(
                      color: Colors.grey[300],
                      child: const Icon(
                        Icons.person,
                        color: Colors.grey,
                        size: 50,
                      ),
                    ),
              ),
            ),
          ),
        ),
      ),
    );
  }

}
