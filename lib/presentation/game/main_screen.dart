import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../logic/cubit/game_cubit.dart';
import '../../logic/cubit/game_state.dart';
import '../../logic/service/audio_service.dart';
import 'main_menu_screen.dart';
import '../../logic/service/effect_service.dart';
import '../../logic/service/score_service.dart';
import 'widgets/progress_and_effects_bar.dart';
import 'widgets/swipe_card_area.dart';
import 'widgets/character_name.dart';
import 'widgets/swipe_text_area.dart';
import '../../logic/service/ads_service.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with TickerProviderStateMixin {
  final AudioService _audioService = AudioService();
  final EffectService _effectService = EffectService(ScoreService());
  final AdsService _adsService = AdsService();
  bool _effectsInitialized = false;

  // Animation controllers - chỉ giữ animation, không giữ state
  late AnimationController _resetController;
  late Animation<Offset> _resetAnimation;

  late AnimationController _swipeOutController;
  late Animation<Offset> _swipeOutAnimation;

  late AnimationController _showCardController;
  late Animation<double> _fadeInAnimation;
  late Animation<double> _scaleInAnimation;

  late AnimationController _fadeOutController;
  late Animation<double> _fadeOutAnimation;

  bool _isGameOver = false;
  String? _currentCardId;

  @override
  void initState() {
    super.initState();
    context.read<GameCubit>().startGame();
    _initGameMusic();

    _resetController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    )..addListener(() {
      final gameCubit = context.read<GameCubit>();
      gameCubit.onResetAnimationUpdate(_resetAnimation.value);
    })..addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        context.read<GameCubit>().onResetAnimationCompleted();
      }
    });

    _swipeOutController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    )..addListener(() {
      final gameCubit = context.read<GameCubit>();
      gameCubit.onSwipeAnimationUpdate(_swipeOutAnimation.value);
    })..addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        context.read<GameCubit>().onSwipeAnimationCompleted();
      }
    });

    _showCardController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _fadeInAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _showCardController, curve: Curves.easeOut),
    );

    _scaleInAnimation = Tween<double>(begin: 1.03, end: 1.0).animate(
      CurvedAnimation(parent: _showCardController, curve: Curves.easeOut),
    );

    _fadeOutController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _fadeOutAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _fadeOutController, curve: Curves.easeOut),
    );
  }

  Future<void> _initGameMusic() async {
    await _audioService.init();
    await _audioService.playLoop('playgame.mp4');
    await _effectService.init();
    if (mounted) {
      setState(() {
        _effectsInitialized = true;
      });
    }
  }

  @override
  void dispose() {
    _resetController.dispose();
    _swipeOutController.dispose();
    _showCardController.dispose();
    _fadeOutController.dispose();
    super.dispose();
  }

  void _onPanUpdate(DragUpdateDetails details) {
    context.read<GameCubit>().updateCardPosition(details.delta);
  }

  void _onPanEnd(DragEndDetails details) {
    final screenWidth = MediaQuery.of(context).size.width;
    context.read<GameCubit>().handleSwipeEnd(screenWidth);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textScaler = MediaQuery.of(context).textScaler;
    const baseSize = 17.0;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: FadeTransition(
        opacity: _fadeOutAnimation,
        child: SafeArea(
          child: BlocListener<GameCubit, GameState>(
            listener: (context, state) {
              if (state is GameOver) {
                if (!_isGameOver) {
                  _isGameOver = true;
                  _audioService.stopBackgroundMusic();
                  _adsService.showInterstitialAd(
                    onComplete: () {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        Navigator.of(context).pushReplacement(
                          PageRouteBuilder(
                            pageBuilder: (context, animation, secondaryAnimation) => const MainMenuScreen(),
                            transitionsBuilder: (context, animation, secondaryAnimation, child) {
                              return FadeTransition(
                                opacity: animation,
                                child: child,
                              );
                            },
                            transitionDuration: const Duration(seconds: 2),
                          ),
                        );
                        _audioService.playLoop('background.mp4');
                      });
                    },
                    onFailed: () {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        Navigator.of(context).pushReplacement(
                          PageRouteBuilder(
                            pageBuilder: (context, animation, secondaryAnimation) => const MainMenuScreen(),
                            transitionsBuilder: (context, animation, secondaryAnimation, child) {
                              return FadeTransition(
                                opacity: animation,
                                child: child,
                              );
                            },
                            transitionDuration: const Duration(seconds: 2),
                          ),
                        );
                        _audioService.playLoop('background.mp4');
                      });
                    },
                  );
                }
              }

              if (state is GamePlaying) {
                // Handle animation triggers
                if (state.shouldStartSwipeAnimation && state.swipeEndOffset != null) {
                  _swipeOutAnimation = Tween<Offset>(
                    // Bắt đầu từ vị trí hiện tại (có cả dx, dy)
                    begin: state.cardOffset,
                    // Kết thúc: X ra ngoài, Y giữ nguyên
                    end: Offset(
                      state.swipeEndOffset!.dx,
                      state.cardOffset.dy,
                    ),
                  ).animate(
                    CurvedAnimation(
                      parent: _swipeOutController,
                      curve: Curves.easeOut,
                    ),
                  );

                  _swipeOutController.forward(from: 0.0);
                  _audioService.playSwipeSound();
                }

                if (state.shouldStartResetAnimation) {
                  _resetAnimation = Tween<Offset>(
                    begin: state.cardOffset,
                    end: Offset.zero,
                  ).animate(
                    CurvedAnimation(parent: _resetController, curve: Curves.easeOut),
                  );
                  _resetController.forward(from: 0.0);
                }

                // Handle card changes
                if (_currentCardId != state.currentCard.id) {
                  _currentCardId = state.currentCard.id;
                  _showCardController.forward(from: 0.0);
                }
              }
            },
            child: BlocBuilder<GameCubit, GameState>(
              builder: (context, state) {
                if (state is GameInitial) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (state is GamePlaying) {
                  final card = state.currentCard;

                  final isCritical = state.internalTrust <= 0 ||
                      state.externalTrust <= 0 ||
                      state.mentalStability <= 0 ||
                      state.socialLinks <= 0;

                  // Tính preview effects từ state
                  Map<String, int> previewEffects = <String, int>{};
                  if (state.cardOffset.dx < -20) {
                    previewEffects = card.leftEffectMap;
                  } else if (state.cardOffset.dx > 20) {
                    previewEffects = card.rightEffectMap;
                  }

                  return Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // Progress icons and effects bar
                      Flexible(
                        flex: 3,
                        child: ProgressAndEffectsBar(
                          state: state,
                          effectsInitialized: _effectsInitialized,
                          effectService: _effectService,
                        ),
                      ),
                      // Main card area
                      Expanded(
                        flex: 12,
                        child: AnimatedContainer(
                          duration: const Duration(seconds: 3),
                          color: isCritical
                              ? theme.scaffoldBackgroundColor
                              : const Color(0xFF9c9389),
                          width: double.infinity,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Card description
                              Flexible(
                                flex: 2,
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 10),
                                  child: Center(
                                    child: SingleChildScrollView(
                                      child: DefaultTextStyle.merge(
                                        style: TextStyle(
                                          color: isCritical
                                              ? Colors.white
                                              : theme.scaffoldBackgroundColor,
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(vertical: 5),
                                          child: Text(
                                            card.description,
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              fontWeight: FontWeight.normal,
                                              fontSize: textScaler.scale(baseSize),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              // Card swipe area
                              Flexible(
                                flex: 6,
                                child: SwipeCardArea(
                                  state: state,
                                  onPanUpdate: _onPanUpdate,
                                  onPanEnd: _onPanEnd,
                                  fadeInAnimation: _fadeInAnimation,
                                  scaleInAnimation: _scaleInAnimation,
                                ),
                              ),
                              // Character name
                              Flexible(
                                flex: 1,
                                child: CharacterName(
                                  card: card,
                                  isCritical: isCritical,
                                  textScaler: textScaler,
                                  baseSize: baseSize,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      // Swipe text area
                      Flexible(
                        flex: 2,
                        child: SwipeTextArea(
                          swipeText: state.swipeText,
                          audioService: _audioService,
                          textScaler: textScaler,
                          baseSize: baseSize,
                        ),
                      ),
                    ],
                  );
                }

                return const Center(
                  child: Text("", style: TextStyle(color: Colors.white)),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
