import 'package:flutter/material.dart';

class CharacterName extends StatelessWidget {
  final dynamic card;
  final bool isCritical;
  final TextScaler textScaler;
  final double baseSize;

  const CharacterName({
    super.key,
    required this.card,
    required this.isCritical,
    required this.textScaler,
    required this.baseSize,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.only(top: 5, bottom: 5),
        child: SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            child: Text(
              card.character != null
                  ? card.character!.name
                  : '<PERSON>hông có nhân vật',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: isCritical
                    ? Colors.white
                    : theme.scaffoldBackgroundColor,
                fontSize: textScaler.scale(baseSize),
                fontStyle: FontStyle.normal,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
