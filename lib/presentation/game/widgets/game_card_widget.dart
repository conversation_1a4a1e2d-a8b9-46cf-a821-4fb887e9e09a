import 'package:flutter/material.dart';
import '../../../logic/cubit/game_state.dart';

class GameCardWidget extends StatelessWidget {
  final dynamic card;
  final GamePlaying state;
  final Animation<double> fadeInAnimation;
  final Animation<double> scaleInAnimation;

  const GameCardWidget({
    super.key,
    required this.card,
    required this.state,
    required this.fadeInAnimation,
    required this.scaleInAnimation,
  });

  @override
  Widget build(BuildContext context) {
    final containerHeight = MediaQuery.of(context).size.height * 0.67;
    final cardHeight = containerHeight * 0.67;
    final cardWidth = cardHeight * (1.1 / 1.3);

    return FadeTransition(
      opacity: fadeInAnimation,
      child: ScaleTransition(
        scale: scaleInAnimation,
        child: Transform.translate(
          offset: state.cardOffset,
          child: Transform.rotate(
            angle: state.isCardAnimatingOut ? state.swipeOutRotation : state.cardOffset.dx * 0.002,
            child: Container(
              width: cardWidth,
              height: cardHeight,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child: card.character != null && card.character!.imgUrl.isNotEmpty
                    ? Image.asset(
                        card.character!.imgUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey[300],
                            child: const Icon(
                              Icons.person,
                              color: Colors.grey,
                              size: 50,
                            ),
                          );
                        },
                      )
                    : Container(
                        color: Colors.grey[300],
                        child: const Icon(
                          Icons.person,
                          color: Colors.grey,
                          size: 50,
                        ),
                      ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
