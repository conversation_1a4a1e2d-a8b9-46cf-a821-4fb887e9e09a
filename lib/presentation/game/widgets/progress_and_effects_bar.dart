import 'package:flutter/material.dart';
import '../../../logic/cubit/game_state.dart';
import '../../../logic/service/effect_service.dart';
import 'progress_icon.dart';

class ProgressAndEffectsBar extends StatelessWidget {
  final GamePlaying state;
  final bool effectsInitialized;
  final EffectService effectService;

  const ProgressAndEffectsBar({
    super.key,
    required this.state,
    required this.effectsInitialized,
    required this.effectService,
  });

  @override
  Widget build(BuildContext context) {
    final card = state.currentCard;

    // Tính preview effects từ state
    Map<String, int> previewEffects = <String, int>{};
    if (state.cardOffset.dx < -20) {
      previewEffects = card.leftEffectMap;
    } else if (state.cardOffset.dx > 20) {
      previewEffects = card.rightEffectMap;
    }

    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      width: double.infinity,
      child: Center(
        child: Padding(
          padding: EdgeInsets.symmetric(
              horizontal: MediaQuery.of(context).size.width * 0.12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 15),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ProgressIcon(
                      progress: state.internalTrust / 10,
                      icon: Icons.remove_red_eye,
                      fillColor: Colors.white,
                      showDot: previewEffects.containsKey('internalTrust') &&
                          (previewEffects['internalTrust']!).abs() > 0,
                      bigDot: previewEffects.containsKey('internalTrust') &&
                          (previewEffects['internalTrust']!).abs() >= 3,
                    ),
                    ProgressIcon(
                      progress: state.externalTrust / 10,
                      icon: Icons.gps_fixed,
                      fillColor: Colors.white,
                      showDot: previewEffects.containsKey('externalTrust') &&
                          (previewEffects['externalTrust']!).abs() > 0,
                      bigDot: previewEffects.containsKey('externalTrust') &&
                          (previewEffects['externalTrust']!).abs() >= 3,
                    ),
                    ProgressIcon(
                      progress: state.mentalStability / 10,
                      icon: Icons.heart_broken,
                      fillColor: Colors.white,
                      showDot: previewEffects.containsKey('mentalStability') &&
                          (previewEffects['mentalStability']!).abs() > 0,
                      bigDot: previewEffects.containsKey('mentalStability') &&
                          (previewEffects['mentalStability']!).abs() >= 3,
                    ),
                    ProgressIcon(
                      progress: state.socialLinks / 10,
                      icon: Icons.groups_3,
                      fillColor: Colors.white,
                      showDot: previewEffects.containsKey('socialLinks') &&
                          (previewEffects['socialLinks']!).abs() > 0,
                      bigDot: previewEffects.containsKey('socialLinks') &&
                          (previewEffects['socialLinks']!).abs() >= 3,
                    ),
                  ],
                ),
              ),
              // Effects display
              Flexible(
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 5),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: MediaQuery.of(context).size.width * 0.42,
                        height: 23,
                        decoration: BoxDecoration(
                          color: Colors.transparent,
                          border: Border.all(color: Colors.white),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: effectsInitialized
                            ? Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  for (var effect in [
                                    {'title': 'Eye', 'icon': 'assets/icons/eye.webp'},
                                    {'title': 'Military badge', 'icon': 'assets/icons/star.webp'},
                                    {'title': 'Heart', 'icon': 'assets/icons/heart.webp'},
                                    {'title': 'Newspaper', 'icon': 'assets/icons/newspaper.webp'},
                                  ])
                                    if (effectService.getEffectQuantity(effect['title']!) > 0)
                                      Padding(
                                        padding: const EdgeInsets.symmetric(horizontal: 4),
                                        child: Image.asset(
                                          effect['icon']!,
                                          width: 15,
                                          height: 15,
                                        ),
                                      ),
                                ],
                              )
                            : null,
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
