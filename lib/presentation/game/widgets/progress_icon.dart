import 'package:flutter/material.dart';

class ProgressIcon extends StatefulWidget {
  final double progress;
  final IconData icon;
  final Color fillColor;
  final Color backgroundColor;
  final bool showDot;
  final bool bigDot;

  const ProgressIcon({
    super.key,
    required this.progress,
    required this.icon,
    required this.fillColor,
    this.backgroundColor = const Color(0xFF787878),
    this.showDot = false,
    this.bigDot = false,
  });

  @override
  State<ProgressIcon> createState() => _ProgressIconState();
}

class _ProgressIconState extends State<ProgressIcon> with TickerProviderStateMixin {
  late double _previousProgress;
  late AnimationController _colorController;
  late Animation<Color?> _colorAnimation;
  Color _baseEffectColor = Colors.white;

  @override
  void initState() {
    super.initState();
    _previousProgress = widget.progress;

    // Khởi tạo AnimationController cho việc chuyển màu
    _colorController = AnimationController(
      duration: const Duration(milliseconds: 2000), // 1.5 gi<PERSON>y để chuyển dần
      vsync: this,
    );

    // Khởi tạo animation màu từ màu hiệu ứng về trắng
    _colorAnimation = ColorTween(
      begin: Colors.white,
      end: Colors.white,
    ).animate(CurvedAnimation(
      parent: _colorController,
      curve: Curves.easeOut,
    ));

    _colorController.addListener(() {
      setState(() {});
    });
  }

  @override
  void didUpdateWidget(ProgressIcon oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.progress != _previousProgress) {
      // Xác định màu dựa trên sự thay đổi của progress
      _baseEffectColor = widget.progress > _previousProgress
          ? Colors.green // Màu xanh khi tăng
          : Colors.red;  // Màu đỏ khi giảm

      // Thiết lập animation từ màu hiệu ứng về trắng
      _colorAnimation = ColorTween(
        begin: _baseEffectColor,
        end: Colors.white,
      ).animate(CurvedAnimation(
        parent: _colorController,
        curve: Curves.easeOut,
      ));

      // Reset và bắt đầu animation
      _colorController.reset();
      _colorController.forward();

      _previousProgress = widget.progress;
    }
  }

  @override
  void dispose() {
    _colorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topCenter,
      clipBehavior: Clip.none,
      children: [
        // ProgressIcon với animation màu mượt mà
        TweenAnimationBuilder<double>(
          duration: const Duration(milliseconds: 1000),
          tween: Tween<double>(begin: 0, end: widget.progress),
          builder: (context, animatedProgress, child) {
            return ShaderMask(
              shaderCallback: (bounds) {
                return LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  stops: [animatedProgress, animatedProgress],
                  colors: [_colorAnimation.value ?? Colors.white, widget.backgroundColor],
                ).createShader(bounds);
              },
              blendMode: BlendMode.srcIn,
              child: Icon(
                widget.icon,
                size: 35,
                color: Colors.white,
              ),
            );
          },
        ),
        // Dot indicators
        if(widget.showDot && !widget.bigDot)
          const Positioned(
            top: -15,
            child: CircleAvatar(
              radius: 2.5,
              backgroundColor: Colors.white,
            ),
          ),
        if (widget.showDot && widget.bigDot)
          const Positioned(
            top: -15,
            child: CircleAvatar(
              radius: 4.5,
              backgroundColor: Colors.white,
            ),
          ),
      ],
    );
  }
}
