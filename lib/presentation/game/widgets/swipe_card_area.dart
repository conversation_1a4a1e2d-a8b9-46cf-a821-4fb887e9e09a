import 'package:flutter/material.dart';
import '../../../logic/cubit/game_state.dart';
import 'game_card_widget.dart';

class SwipeCardArea extends StatelessWidget {
  final GamePlaying state;
  final void Function(DragUpdateDetails) onPanUpdate;
  final void Function(DragEndDetails) onPanEnd;
  final Animation<double> fadeInAnimation;
  final Animation<double> scaleInAnimation;

  const SwipeCardArea({
    super.key,
    required this.state,
    required this.onPanUpdate,
    required this.onPanEnd,
    required this.fadeInAnimation,
    required this.scaleInAnimation,
  });

  @override
  Widget build(BuildContext context) {
    final card = state.currentCard;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        image: const DecorationImage(
          image: AssetImage('assets/images/logo.webp'),
          fit: BoxFit.cover,
          opacity: 0.5,
        ),
      ),
      child: GestureDetector(
        onPanUpdate: onPanUpdate,
        onPanEnd: onPanEnd,
        child: state.isCardAnimatingOut
            ? Transform.translate(
                offset: state.cardOffset,
                child: Transform.rotate(
                  angle: state.swipeOutRotation,
                  child: GameCardWidget(
                    card: card,
                    state: state,
                    fadeInAnimation: fadeInAnimation,
                    scaleInAnimation: scaleInAnimation,
                  ),
                ),
              )
            : GameCardWidget(
                card: card,
                state: state,
                fadeInAnimation: fadeInAnimation,
                scaleInAnimation: scaleInAnimation,
              ),
      ),
    );
  }
}
