import 'package:flutter/material.dart';
import '../../../logic/service/audio_service.dart';
import '../../settings/settings_screen.dart';

class SwipeTextArea extends StatelessWidget {
  final String swipeText;
  final TextScaler textScaler;
  final double baseSize;
  final AudioService audioService;

  const SwipeTextArea({
    super.key,
    required this.swipeText,
    required this.textScaler,
    required this.baseSize,
    required this.audioService,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      alignment: Alignment.center,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          audioService.playSound(SoundType.buttonClick);
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const SettingsScreen(),
            ),
          );
        },
        child: AnimatedOpacity(
          duration: const Duration(milliseconds: 100),
          opacity: swipeText.isNotEmpty ? 1.0 : 0.0,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Center(
              child: Text(
                swipeText,
                textAlign: TextAlign.center,
                softWrap: true,
                overflow: TextOverflow.ellipsis,
                maxLines: 3,
                style: TextStyle(
                  fontSize: textScaler.scale(baseSize * 0.9),
                  color: Colors.white70,
                  fontWeight: FontWeight.normal,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
