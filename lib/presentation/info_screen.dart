import 'package:flutter/material.dart';

import '../logic/service/audio_service.dart';

class InfoScreen extends StatelessWidget {
  const InfoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    const primaryDark = Color(0xFF08121e);

    return Scaffold(
      backgroundColor: primaryDark,
      body: Safe<PERSON>rea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Thông tin',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () { 
                     AudioService().playSound(SoundType.buttonClick); 
                     Navigator.of(context).pop();
                    },
                  ),
                ],
              ),
              const SizedBox(height: 30),
              // <PERSON>ần nội dung thông tin
              const Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Được phát triển và xây dựng bởi:',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        '- BachLT, ChienLV',
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                      SizedBox(height: 30),
                      Text(
                        'Các nhân vật được thiết kế bởi:',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        '- BachLT',
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                      SizedBox(height: 30),
                      Text(
                        'Hiệu ứng âm thanh bởi:',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        '- BachLT, KhanhNQ',
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                      SizedBox(height: 30),
                      Text(
                        'Cách chơi:',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Bạn sẽ vào vai tình báo chiến lược của Việt Nam, khoác lên mình vỏ bọc một nhà báo quốc tế. Với cây bút và đôi mắt sắc bén, bạn sẽ thâm nhập sâu vào bộ máy đối phương, âm thầm thu thập những thông tin tuyệt mật, quyết định vận mệnh của cả cuộc chiến.',
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                      SizedBox(height: 5),
                      Text(
                        'Những tình huống sẽ được đưa ra dưới dạng thẻ bài, nhiệm vụ của bạn là quẹt trái hoặc quẹt phải để xử lý mỗi tình huống tương ứng.',
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                      SizedBox(height: 5),
                      Text(
                        'Mỗi quyết định quẹt trái hay quẹt phải của bạn sẽ ảnh hưởng trực tiếp tới 4 chỉ số đó là:',
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                      SizedBox(height: 3),
                      Text(
                        '- Niềm tin phe ta.',
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                      Text(
                        '- Niềm tin phe địch.',
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                      Text(
                        '- Tâm lý',
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                      Text(
                        '- Những mối quan hệ xã hội.',
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
