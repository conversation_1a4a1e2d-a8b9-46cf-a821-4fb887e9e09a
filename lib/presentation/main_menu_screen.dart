import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:spy/logic/service/score_service.dart';
import 'package:spy/presentation/game/main_screen.dart';
import 'package:spy/presentation/settings/settings_screen.dart';

import '../data/repositories/card_repository.dart';
import '../logic/cubit/game_cubit.dart';
import '../logic/service/audio_service.dart';

class MainMenuScreen extends StatefulWidget {
  const MainMenuScreen({super.key});

  @override
  State<MainMenuScreen> createState() => _MainMenuScreenState();
}

class _MainMenuScreenState extends State<MainMenuScreen>
    with WidgetsBindingObserver {
  final AudioService _audioService = AudioService();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initBackgroundMusic();
  }

  Future<void> _initBackgroundMusic() async {
    await _audioService.init();
    await _audioService.playLoop('background.mp4');
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _audioService.playLoop('background.mp4');
    } else if (state == AppLifecycleState.paused) {
      _audioService.stopBackgroundMusic();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    // Không dispose audio service ở đây
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Main content - Title and Button
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/images/logo_spy.webp',
                  width: MediaQuery.of(context).size.width * 0.4,
                  height: MediaQuery.of(context).size.width * 0.4,
                  fit: BoxFit.contain,
                ),
                SizedBox(height: MediaQuery.of(context).size.height * 0.05),
                Text(
                  'PERFECT SPY',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 28,
                    fontWeight: FontWeight.w900,
                    letterSpacing: 4,
                    height: 1,
                    shadows: [
                      Shadow(
                        color: Colors.black.withValues(alpha: 0.6),
                        offset: const Offset(3, 3),
                        blurRadius: 5,
                      ),
                      Shadow(
                        color: Theme.of(context).primaryColor.withValues(alpha: 0.4),
                        offset: const Offset(-2, -2),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 100),
                CustomButton(
                  onPressed: () {
                    _audioService.playSound(SoundType.buttonClick); // Thêm âm thanh cho button tiếp tục
                    _audioService.stopBackgroundMusic(); // Chỉ dừng nhạc, không dispose
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(
                        builder: (context) => BlocProvider(
                          create: (_) => GameCubit(CardRepository(), ScoreService()),
                          child: const MainScreen(),
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          // Settings icon at top center
          Positioned(
            top: 40, // Khoảng cách từ top
            left: 0,
            right: 0,
            child: Center(
              child: IconButton(
                icon: const Icon(
                  Icons.settings,
                  color: Colors.white,
                  size: 30,
                ),
                onPressed: () {
                  _audioService.playSound(SoundType.buttonClick); // Thêm âm thanh
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (_) => const SettingsScreen(),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class CustomButton extends StatelessWidget {
  final VoidCallback onPressed;

  const CustomButton({
    super.key,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;

    return Stack(
      alignment: Alignment.center,
      children: [
        // Container nền chạy hết màn hình
        Container(
          width: screenWidth,
          height: 100,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary,
            border: Border(
              top: BorderSide(
                color: Colors.white.withValues(alpha: 0.5),
                width: 2,
              ),
              bottom: BorderSide(
                color: Colors.white.withValues(alpha: 0.5),
                width: 2,
              ),
            ),
          ),
        ),
        // Button chính giữa
        MaterialButton(
          onPressed: onPressed,
          child: const Text(
            'TIẾP TỤC',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w500,
              letterSpacing: 1.5,
            ),
          ),
        ),
      ],
    );
  }
}
