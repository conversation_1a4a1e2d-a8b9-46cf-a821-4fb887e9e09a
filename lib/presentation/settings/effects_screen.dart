import 'package:flutter/material.dart';
import 'package:spy/data/models/effect.dart';
import 'package:spy/logic/service/notification_service.dart';
import '../../logic/service/score_service.dart';
import '../../logic/service/effect_service.dart';
import '../../logic/service/audio_service.dart';
import '../ads/watch_ads_button.dart';

class EffectsScreen extends StatefulWidget {
  const EffectsScreen({super.key});

  @override
  State<EffectsScreen> createState() => _EffectsScreenState();
}

class _EffectsScreenState extends State<EffectsScreen> {
  final ScoreService _scoreService = ScoreService();
  late final EffectService _effectService;
  final AudioService _audioService = AudioService();
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initServices();
  }

  Future<void> _initServices() async {
    await _scoreService.init();
    _effectService = EffectService(_scoreService);
    await _effectService.init();
    setState(() {
      _isInitialized = true;
    });
  }

  final List<Effect> effects = [
    Effect(
      icon: 'assets/icons/eye.webp',
      title: 'Eye',
      description: 'Niềm tin từ tổ chức đang cạn dần, món quà này sẽ giúp bạn phục hồi lại niềm tin từ tổ chức.',
      price: 1900,
      quantity: 0,
    ),
    Effect(
      icon: 'assets/icons/star.webp',
      title: 'Military badge',
      description: 'Huy hiệu từ tướng chỉ huy cấp cao. Nó sẽ cứu sống bạn trong những tình huống nguy cấp khi phải đối mặt với những tay tình báo từ phe địch',
      price: 1900,
      quantity: 0,
    ),
    Effect(
      icon: 'assets/icons/heart.webp',
      title: 'Heart',
      description: 'Một trái tim khỏe mạnh sẽ giữ cho bạn một tinh thần khỏe mạnh.',
      price: 1900,
      quantity: 0,
    ),
    Effect(
      icon: 'assets/icons/newspaper.webp',
      title: 'Newspaper',
      description: 'Một bài báo đính chính kịp thời sẽ giúp bạn đập tan mọi nghi ngờ.',
      price: 1900,
      quantity: 0,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Center(child: CircularProgressIndicator());
    }
    
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: Stack(
        children: [
          // Danh sách effects
          ListView.builder(
            padding: const EdgeInsets.only(top: 60, left: 16, right: 16), // Thêm padding top để không bị che bởi coin display
            itemCount: effects.length,
            itemBuilder: (context, index) {
              return _buildEffectCard(context, effects[index]);
            },
          ),
          // Coin display
          Positioned(
            top: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: theme.scaffoldBackgroundColor,
                border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
                borderRadius: BorderRadius.circular(5),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min, // Thêm dòng này để container chỉ rộng vừa đủ với nội dung
                children: [
                  WatchAdsButton(
                    onCoinsUpdated: () {
                      if (mounted) {
                        setState(() {});
                      }
                    },
                  ),
                  const SizedBox(width: 8),
                  FutureBuilder<int>(
                    future: _scoreService.getCoin(),
                    builder: (context, snapshot) {
                      if (snapshot.hasData) {
                        return Text(
                          '${snapshot.data}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        );
                      }
                      return const Text(
                        '0',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    },
                  ),
                  const SizedBox(width: 4),
                  const Icon(
                    Icons.monetization_on,
                    color: Colors.amber,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEffectCard(BuildContext context, Effect effect) {
    final quantity = _effectService.getEffectQuantity(effect.title);
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 24), // Tăng padding từ 16 lên 24 để tạo khoảng cách 10% thêm
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Icon
              Image.asset(
                effect.icon,
                width: 35,
                height: 35,
              ),
              const SizedBox(width: 16),
              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      effect.title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      effect.description,
                      style: const TextStyle(
                        color: Colors.white70,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Bạn có: $quantity',
                      style: const TextStyle(
                        color: Colors.white70,
                      ),
                    ),
                    const SizedBox(height: 12),
                    // Buy button
                    InkWell(
                      onTap: () {
                        _audioService.playSound(SoundType.buttonClick); // Thêm âm thanh
                        _showBuyConfirmation(context, effect);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          vertical: 8,
                          horizontal: 24,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.white),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: const Text(
                          'Mua',
                          style: TextStyle(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        // Divider line
        if (effect != effects.last) // Chỉ thêm divider nếu không phải item cuối
          Container(
            height: 1,
            color: Colors.white.withValues(alpha: 0.3),
          ),
      ],
    );
  }

  void _showBuyConfirmation(BuildContext context, Effect effect) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Theme.of(context).primaryColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5),
          side: const BorderSide(color: Colors.white),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Xác nhận mua vật phẩm',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 10),
              Text(
                'Giá: ${effect.price}',
                style: const TextStyle(
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Confirm button
                  InkWell(
                    onTap: () async {
                      _audioService.playSound(SoundType.buttonClick);
                      final success = await _effectService.buyEffect(effect);
                      if (context.mounted) {
                        if (success) {
                          Navigator.of(context).pop();
                          setState(() {});
                          NotificationService.showMessage(context, 'Mua vật phẩm thành công!');
                        } else {
                          NotificationService.showMessage(context, 'Không đủ coin để mua vật phẩm!');
                        }
                      }
                    },
                    child: SizedBox(
                      width: 120, // Đặt chiều rộng cố định cho cả 2 button
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          vertical: 8,
                          horizontal: 20,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.white),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: const Center(
                          child: Text(
                            'Xác nhận',
                            style: TextStyle(
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  // Cancel button
                  const SizedBox(width: 2),
                  InkWell(
                    onTap: () {
                      _audioService.playSound(SoundType.buttonClick);
                      Navigator.of(context).pop();
                    },
                    child: SizedBox(
                      width: 120, // Đặt chiều rộng cố định cho cả 2 button
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          vertical: 8,
                          horizontal: 20,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.white),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: const Center(
                          child: Text(
                            'Từ chối',
                            style: TextStyle(
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
