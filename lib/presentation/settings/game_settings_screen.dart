import 'package:flutter/material.dart';
import '../../logic/service/audio_service.dart';
import 'package:spy/presentation/info_screen.dart';

class GameSettingsScreen extends StatefulWidget {
  const GameSettingsScreen({super.key});

  @override
  State<GameSettingsScreen> createState() => _GameSettingsScreenState();
}

class _GameSettingsScreenState extends State<GameSettingsScreen> {
  bool _musicEnabled = true;
  bool _soundEnabled = true;
  final AudioService _audioService = AudioService();

  @override
  void initState() {
    super.initState();
    _loadAudioSettings();
  }

  Future<void> _loadAudioSettings() async {
    await _audioService.init();
    setState(() {
      _musicEnabled =
          _audioService.settingsBox.get('isSoundEnabled', defaultValue: true);
      _soundEnabled =
          _audioService.settingsBox.get('isEffectEnabled', defaultValue: true);
    });
  }

  Future<void> _toggleBackgroundMusic(bool value) async {
    await _audioService.settingsBox.put('isSoundEnabled', value);
    setState(() => _musicEnabled = value);

    if (!value) {
      await _audioService.stopBackgroundMusic();
    } else {
      // Kiểm tra route gốc (route phía dưới settings screen)
      // ignore: use_build_context_synchronously
      final currentContext = Navigator.of(context).context;
      // ignore: use_build_context_synchronously
      final currentRoute = ModalRoute.of(currentContext)?.settings.name;

      // Phát nhạc tương ứng với màn hình hiện tại
      if (currentRoute?.contains('game') ?? false) {
        await _audioService.playLoop('playgame.mp4');
      } else {
        await _audioService.playLoop('background.mp4');
      }
    }
  }

  Future<void> _toggleSoundEffects(bool value) async {
    await _audioService.settingsBox.put('isEffectEnabled', value);
    setState(() => _soundEnabled = value);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSettingSection(
            title: 'Âm thanh',
            children: [
              _buildSwitchTile(
                title: 'Nhạc nền',
                value: _musicEnabled,
                onChanged: (value) async {
                  await _toggleBackgroundMusic(value);
                },
              ),
              _buildSwitchTile(
                title: 'Âm thanh',
                value: _soundEnabled,
                onChanged: (value) async {
                  await _toggleSoundEffects(value);
                },
              ),
            ],
          ),
          _buildInfoButton(),
        ],
      ),
    );
  }

  Widget _buildSettingSection({
    required String title,
    required List<Widget> children,
  }) {
    final theme = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
        color: theme.primaryColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors
                    .white, // Thêm màu trắng cho title section (Âm thanh, Hiển thị)
              ),
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      title: Text(
        title,
        style: const TextStyle(
          color: Colors.white, // Thêm màu trắng cho text của switch tiles
        ),
      ),
      value: value,
      onChanged: onChanged,
      activeColor: Colors.white,
      inactiveThumbColor: Colors.white.withValues(alpha: 0.5),
      inactiveTrackColor: Colors.white.withValues(alpha: 0.2),
    );
  }

  Widget _buildInfoButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20.0),
      child: OutlinedButton(
        onPressed: () {
          _audioService.playSound(SoundType.buttonClick); // Thêm âm thanh click
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const InfoScreen()),
          );
        },
        style: OutlinedButton.styleFrom(
          side: const BorderSide(color: Colors.white),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5.0),
          ),
        ),
        child: const Text(
          'Thông tin',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }
}
