import 'package:flutter/material.dart';
import 'package:spy/logic/service/ads_service.dart';
import 'package:spy/logic/service/notification_service.dart';
import '../../logic/service/audio_service.dart';
import 'package:spy/presentation/settings/info_screen.dart';
import '../../logic/service/iap_service.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'dart:async';

class GameSettingsScreen extends StatefulWidget {
  const GameSettingsScreen({super.key});

  @override
  State<GameSettingsScreen> createState() => _GameSettingsScreenState();
}

class _GameSettingsScreenState extends State<GameSettingsScreen> {
  bool _musicEnabled = true;
  bool _soundEnabled = true;
  final AudioService _audioService = AudioService();
  bool _showRestoreButton = false;
  bool _showRemoveAdsButton = true;
  StreamSubscription<List<PurchaseDetails>>? _purchaseSubscription;

  @override
  void initState() {
    super.initState();
    _loadAudioSettings();
    _updateButtonVisibility();
    _purchaseSubscription = InAppPurchase.instance.purchaseStream.listen((purchases) {
      for (final purchase in purchases) {
        if ((purchase.status == PurchaseStatus.purchased || purchase.status == PurchaseStatus.restored) &&
            purchase.productID == IAPService().removeAdsProductId) {
          _updateButtonVisibility();
        }
      }
    });
  }

  void _updateButtonVisibility() {
    final adsRemoved = AdsService().isAdsRemoved;
    setState(() {
      _showRestoreButton = adsRemoved;
      _showRemoveAdsButton = !adsRemoved;
    });
  }

  @override
  void dispose() {
    _purchaseSubscription?.cancel();
    super.dispose();
  }

  Future<void> _loadAudioSettings() async {
    await _audioService.init();
    setState(() {
      _musicEnabled =
          _audioService.settingsBox.get('isSoundEnabled', defaultValue: true);
      _soundEnabled =
          _audioService.settingsBox.get('isEffectEnabled', defaultValue: true);
    });
  }

  Future<void> _toggleBackgroundMusic(bool value) async {
    await _audioService.settingsBox.put('isSoundEnabled', value);
    setState(() => _musicEnabled = value);

    if (!value) {
      await _audioService.stopBackgroundMusic();
    } else {
      // Kiểm tra route gốc (route phía dưới settings screen)
      // ignore: use_build_context_synchronously
      final currentContext = Navigator.of(context).context;
      // ignore: use_build_context_synchronously
      final currentRoute = ModalRoute.of(currentContext)?.settings.name;

      // Phát nhạc tương ứng với màn hình hiện tại
      if (currentRoute?.contains('game') ?? false) {
        await _audioService.playLoop('playgame.mp4');
      } else {
        await _audioService.playLoop('background.mp4');
      }
    }
  }

  Future<void> _toggleSoundEffects(bool value) async {
    await _audioService.settingsBox.put('isEffectEnabled', value);
    setState(() => _soundEnabled = value);
  }

Widget _buildRemoveAdsButton() {
  if (!_showRemoveAdsButton) return const SizedBox.shrink();
  final theme = Theme.of(context);

  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 8.0),
    child: InkWell(
      onTap: () async {
        _audioService.playSound(SoundType.buttonClick);

        try {
          await IAPService.instance.buyRemoveAds();
        } catch (e) {
          if (mounted) {
            NotificationService.showMessage(context, 'Giao dịch chưa thành công!');
          }
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: theme.primaryColor,
          border: Border.all(color: Colors.white, width: 2),
          borderRadius: BorderRadius.circular(8),
        ),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        child: Row(
          children: [
            Image.asset(
              'assets/images/logo_spy.webp',
              width: 32,
              height: 32,
            ),
            const SizedBox(width: 8),
            const Expanded(
              child: Center(
                child: Text(
                  'REMOVE ADS',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            FutureBuilder<String?>(
              future: IAPService.instance.getProductPriceString("remove_ads"),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Text("...");
                }
                if (!snapshot.hasData) {
                  return const Text("N/A");
                }
                return Text(
                  snapshot.data!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    ),
  );
}

  Widget _buildRestoreButton() {
    if (!_showRestoreButton) return const SizedBox.shrink();
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: InkWell(
        onTap: () async {
          _audioService.playSound(SoundType.buttonClick);
          try {
            await IAPService().restorePurchases();
          } catch (e) {
            if (mounted) {
              NotificationService.showMessage(context,'Khôi phục thất bại!');
            }
          }
        },
        child: Container(
          decoration: BoxDecoration(
            color: theme.primaryColor,
            border: Border.all(color: Colors.white, width: 2),
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          child: const Row(
            children: [
              Icon(Icons.restore, color: Colors.white, size: 32),
              SizedBox(width: 8),
              Expanded(
                child: Center(
                  child: Text(
                    'Restore Purchases',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSettingSection(
            title: 'Âm thanh',
            children: [
              _buildSwitchTile(
                title: 'Nhạc nền',
                value: _musicEnabled,
                onChanged: (value) async {
                  await _toggleBackgroundMusic(value);
                },
              ),
              _buildSwitchTile(
                title: 'Âm thanh',
                value: _soundEnabled,
                onChanged: (value) async {
                  await _toggleSoundEffects(value);
                },
              ),
            ],
          ),
          _buildRemoveAdsButton(),
          _buildRestoreButton(),
          _buildInfoButton(),
        ],
      ),
    );
  }

  Widget _buildSettingSection({
    required String title,
    required List<Widget> children,
  }) {
    final theme = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
        color: theme.primaryColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors
                    .white, // Thêm màu trắng cho title section (Âm thanh, Hiển thị)
              ),
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      title: Text(
        title,
        style: const TextStyle(
          color: Colors.white, // Thêm màu trắng cho text của switch tiles
        ),
      ),
      value: value,
      onChanged: onChanged,
      activeThumbColor: Colors.white,
      inactiveThumbColor: Colors.white.withValues(alpha: 0.5),
      inactiveTrackColor: Colors.white.withValues(alpha: 0.2),
    );
  }

  Widget _buildInfoButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20.0),
      child: OutlinedButton(
        onPressed: () {
          _audioService.playSound(SoundType.buttonClick); // Thêm âm thanh click
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const InfoScreen()),
          );
        },
        style: OutlinedButton.styleFrom(
          side: const BorderSide(color: Colors.white),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5.0),
          ),
        ),
        child: const Text(
          'Thông tin',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }
}
