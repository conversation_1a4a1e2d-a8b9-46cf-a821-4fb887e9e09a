import 'package:flutter/material.dart';
import 'package:spy/data/models/story_line.dart';
import 'package:spy/logic/service/story_service.dart';
import '../../logic/service/character_service.dart';
import '../../data/models/character.dart';

class ProgressScreen extends StatefulWidget {
  const ProgressScreen({super.key});

  @override
  State<ProgressScreen> createState() => _ProgressScreenState();
}

class _ProgressScreenState extends State<ProgressScreen> {
  final CharacterService _characterService = CharacterService();
  final StoryService _storyService = StoryService();

  int _unlockedCharacters = 0;
  int _totalCharacters = 0;
  int _unlockedStoryLines = 0;
  int _totalStoryLines = 0;
  bool _isLoading = true;
  List<Character> _allCharacters = [];
  List<StoryLine> _allStoryLines = [];

  @override
  void initState() {
    super.initState();
    _loadCharacterProgress();
  }

  Future<void> _loadCharacterProgress() async {
    try {
      await _characterService.init();
      final unlocked = await _characterService.getUnlockedCharacterCount();
      final total = await _characterService.getTotalCharacterCount();
      final characters = await _characterService.getAllCharacters();
      final storyLines = await _storyService.getAllStoryLines();

      final unlockedStories = await _storyService.getUnlockedStoryCount();
      final totalStories = await _storyService.getTotalStoryCount();

      setState(() {
        _unlockedCharacters = unlocked;
        _totalCharacters = total;
        _allCharacters = characters;
        _unlockedStoryLines = unlockedStories;
        _totalStoryLines = totalStories;
        _allStoryLines = storyLines;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading character progress: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  double get _characterProgress {
    if (_totalCharacters == 0) return 0.0;
    return _unlockedCharacters / _totalCharacters;
  }

  double get _storyProgress {
    if (_totalStoryLines == 0) return 0.0;
    return _unlockedStoryLines / _totalStoryLines;
  }

  void _showCharacterPopup() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: EdgeInsets.zero,
          child: UnconstrainedBox(
            constrainedAxis: Axis.vertical,
            child: Container(
              width: MediaQuery.of(context).size.width * 0.90,
              height: MediaQuery.of(context).size.height * 0.85,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                children: [
                  // Header
                  Container(
                    padding: const EdgeInsets.all(5),
                    child: Padding(
                      padding: const EdgeInsets.only(left: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Các nhân vật',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.normal,
                              color: Colors.white,
                            ),
                          ),
                          IconButton(
                            onPressed: () => Navigator.of(context).pop(),
                            icon: const Icon(Icons.close, color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Character grid
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: GridView.builder(
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          childAspectRatio: 0.8,
                          crossAxisSpacing: 10,
                          mainAxisSpacing: 0,
                        ),
                        itemCount: _allCharacters.length,
                        itemBuilder: (context, index) {
                          final character = _allCharacters[index];
                          final isUnlocked = character.isUnlocked == 1;

                          return Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // Character image
                                Expanded(
                                  flex: 3,
                                  child: Container(
                                    margin: const EdgeInsets.all(8),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: isUnlocked
                                          ? Image.asset(
                                              character.imgUrl,
                                              fit: BoxFit.cover,
                                              width: double.infinity,
                                              errorBuilder:
                                                  (context, error, stackTrace) {
                                                print(
                                                    'Error loading image: ${character.imgUrl}');
                                                print('Error: $error');
                                                // Try with .webp extension if not already present
                                                String imagePath =
                                                    character.imgUrl;
                                                if (!imagePath
                                                    .endsWith('.webp')) {
                                                  imagePath = '$imagePath.webp';
                                                }
                                                return Image.asset(
                                                  '$imagePath',
                                                  fit: BoxFit.cover,
                                                  width: double.infinity,
                                                  errorBuilder: (context,
                                                      error2, stackTrace2) {
                                                    print(
                                                        'Error loading image with .webp: $imagePath');
                                                    return Container(
                                                      width: double.infinity,
                                                      color: Colors.grey
                                                          .withValues(
                                                              alpha: 0.3),
                                                      child: const Icon(
                                                        Icons.person,
                                                        color: Colors.white54,
                                                        size: 40,
                                                      ),
                                                    );
                                                  },
                                                );
                                              },
                                            )
                                          : Container(
                                              width: double.infinity,
                                              color: Colors.white
                                                  .withValues(alpha: 0.1),
                                            ),
                                    ),
                                  ),
                                ),
                                // Character name
                                Expanded(
                                  flex: 1,
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8),
                                    child: Text(
                                      isUnlocked ? character.name : '???',
                                      style: TextStyle(
                                        color: isUnlocked
                                            ? Colors.white
                                            : Colors.white54,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                      ),
                                      textAlign: TextAlign.center,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _showStoryPopup() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: EdgeInsets.zero,
          child: UnconstrainedBox(
            constrainedAxis: Axis.vertical,
            child: Container(
              width: MediaQuery.of(context).size.width * 0.90,
              height: MediaQuery.of(context).size.height * 0.85,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                children: [
                  // Header
                  Container(
                    padding: const EdgeInsets.all(5),
                    child: Padding(
                      padding: const EdgeInsets.only(left: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Các mục tiêu của mạch truyện',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.normal,
                              color: Colors.white,
                            ),
                          ),
                          IconButton(
                            onPressed: () => Navigator.of(context).pop(),
                            icon: const Icon(Icons.close, color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Story lines list
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: ListView.builder(
                        itemCount: _allStoryLines.length,
                        itemBuilder: (context, index) {
                          final storyLine = _allStoryLines[index];
                          final isUnlocked = storyLine.isUnlocked == 1;

                          return Container(
                            margin: const EdgeInsets.only(bottom: 8),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: isUnlocked
                                ? Border.all(color: Colors.white.withValues(alpha: 0.3), width: 1)
                                : Border.all(color: Colors.white.withValues(alpha: 0.1), width: 1),
                            ),
                            child: ListTile(
                              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                              leading: Container(
                                width: 40,
                                height: 40,
                                decoration: BoxDecoration(
                                  color: isUnlocked
                                    ? Colors.white.withValues(alpha: 0.3)
                                    : Colors.grey.withValues(alpha: 0.3),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Icon(
                                  isUnlocked ? Icons.check_circle : Icons.lock,
                                  color: isUnlocked ? Colors.white : Colors.grey,
                                  size: 24,
                                ),
                              ),
                              title: Text(
                                isUnlocked ? (storyLine.name ?? 'Mục tiêu ${index + 1}') : '???',
                                style: TextStyle(
                                  color: isUnlocked ? Colors.white : Colors.white54,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              subtitle: Text(
                                isUnlocked
                                  ? (storyLine.description ?? 'Không có mô tả')
                                  : 'Mục tiêu chưa được mở khóa',
                                style: TextStyle(
                                  color: isUnlocked ? Colors.white70 : Colors.white38,
                                  fontSize: 14,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              trailing: isUnlocked
                                ? const Icon(
                                    Icons.arrow_forward_ios,
                                    color: Colors.white54,
                                    size: 16,
                                  )
                                : null,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              padding: const EdgeInsets.all(10),
              children: [
                _buildCharacterProgressSection(theme),
                const SizedBox(height: 16),
                _buildStoryProgressSection(theme),
              ],
            ),
    );
  }

  Widget _buildCharacterProgressSection(ThemeData theme) {
    return GestureDetector(
      onTap: _showCharacterPopup,
      child: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: theme.primaryColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Các nhân vật',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Row(
                  children: [
                    Text(
                      '$_unlockedCharacters/$_totalCharacters',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Icon(
                      Icons.arrow_forward_ios,
                      color: Colors.white70,
                      size: 16,
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              height: 8,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(4),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: LinearProgressIndicator(
                  value: _characterProgress,
                  backgroundColor: Colors.transparent,
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${(_characterProgress * 100).toStringAsFixed(0)}% hoàn thành',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.white70,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStoryProgressSection(ThemeData theme) {
    return GestureDetector(
      onTap: _showStoryPopup,
      child: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: theme.primaryColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Các mục tiêu của mạch truyện',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Row(
                  children: [
                    Text(
                      '$_unlockedStoryLines/$_totalStoryLines',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Icon(
                      Icons.arrow_forward_ios,
                      color: Colors.white70,
                      size: 16,
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              height: 8,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(4),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: LinearProgressIndicator(
                  value: _storyProgress,
                  backgroundColor: Colors.transparent,
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${(_storyProgress * 100).toStringAsFixed(0)}% hoàn thành',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.white70,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
