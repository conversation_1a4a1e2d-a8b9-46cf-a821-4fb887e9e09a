import 'package:flutter/material.dart';
import '../../logic/service/score_service.dart';

class ProgressScreen extends StatefulWidget {
  const ProgressScreen({super.key});

  @override
  State<ProgressScreen> createState() => _ProgressScreenState();
}

class _ProgressScreenState extends State<ProgressScreen> {
  final ScoreService _scoreService = ScoreService();
  //int _totalMissions = 0;

  @override
  void initState() {
    super.initState();
    //_loadMissions();
  }

  // Future<void> _loadMissions() async {
  //   await _scoreService.init();
  //   final missions = await _scoreService.getMission();
  //   setState(() {
  //     _totalMissions = missions;
  //   });
  // }

  // double _calculateProgress(int chapterNumber) {
  //   if (_totalMissions == 0) return 0.0;
  //
  //   switch (chapterNumber) {
  //     case 1:
  //       return _totalMissions >= 2 ? 1.0 : _totalMissions / 2;
  //     case 2:
  //       if (_totalMissions <= 2) return 0.0;
  //       final chapter2Missions = _totalMissions - 2;
  //       return chapter2Missions >= 200 ? 1.0 : chapter2Missions / 200;
  //     case 3:
  //       if (_totalMissions <= 202) return 0.0;
  //       final chapter3Missions = _totalMissions - 202;
  //       return chapter3Missions >= 296 ? 1.0 : chapter3Missions / 296;
  //     default:
  //       return 0.0;
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // _buildProgressSection(
        //   title: 'Chương Ⅰ Vỏ bọc hoàn hảo',
        //   progress: _calculateProgress(1),
        //   theme: theme,
        // ),
        // const SizedBox(height: 16),
        // _buildProgressSection(
        //   title: 'Chương Ⅱ Lưỡi dao trong ngòi bút',
        //   progress: _calculateProgress(2),
        //   theme: theme,
        //   isLocked: _calculateProgress(1) < 1.0,
        // ),
        // const SizedBox(height: 16),
        // _buildProgressSection(
        //   title: 'Chương Ⅲ Giữa hai làn đạn',
        //   progress: _calculateProgress(3),
        //   theme: theme,
        //   isLocked: _calculateProgress(2) < 1.0,
        // ),
      ],
    );
  }

  Widget _buildProgressSection({
    required String title,
    required double progress,
    required ThemeData theme,
    bool isLocked = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.primaryColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              if (isLocked)
                const Icon(Icons.lock, color: Colors.grey)
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[700],
            valueColor: AlwaysStoppedAnimation<Color>(
              isLocked ? Colors.grey : Colors.blue,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '${(progress * 100).toInt()}%',
            style: TextStyle(
              color: isLocked ? Colors.grey : Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
