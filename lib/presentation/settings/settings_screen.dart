import 'package:flutter/material.dart';
import 'package:spy/presentation/settings/effects_screen.dart';
import 'package:spy/presentation/settings/progress_screen.dart';
import 'package:spy/presentation/settings/game_settings_screen.dart';

import '../../logic/service/audio_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  int _currentIndex = 0;
  final AudioService _audioService = AudioService();

  final List<Widget> _screens = [
    const ProgressScreen(),
    const EffectsScreen(),
    const GameSettingsScreen(),
  ];

  final List<String> _titles = [
    'Tiến trình',
    'Hiệu ứng',
    'Cài đặt',
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Top bar
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              color: theme.primaryColor,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _titles[_currentIndex],
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () {
                      _audioService.playSound(SoundType.buttonClick); // Thêm âm thanh
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              ),
            ),
            
            // Content area
            Expanded(
              child: _screens[_currentIndex],
            ),
            
            // Bottom navigation
            Container(
              padding: const EdgeInsets.symmetric(vertical: 20),
              color: theme.primaryColor,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildNavButton(
                    context: context,
                    icon: Icons.military_tech,
                    label: 'Tiến trình',
                    isSelected: _currentIndex == 0,
                    onTap: () => _switchScreen(0),
                  ),
                  _buildNavButton(
                    context: context,
                    icon: Icons.remove_red_eye,
                    label: 'Hiệu ứng',
                    isSelected: _currentIndex == 1,
                    onTap: () => _switchScreen(1),
                  ),
                  _buildNavButton(
                    context: context,
                    icon: Icons.settings,
                    label: 'Cài đặt',
                    isSelected: _currentIndex == 2,
                    onTap: () => _switchScreen(2),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _switchScreen(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  Widget _buildNavButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: () {
        _audioService.playSound(SoundType.buttonClick); // Thêm âm thanh
        onTap();
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 32,
            color: isSelected ? Colors.blue : Colors.white,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: isSelected ? Colors.blue : Colors.white,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}

