import 'package:flutter/material.dart';

class ProgressIcon extends StatefulWidget {
  final double progress;
  final IconData icon;
  final Color fillColor;
  final Color backgroundColor;
  final bool showDot;
  final bool bigDot;

  const ProgressIcon({
    super.key,
    required this.progress,
    required this.icon,
    required this.fillColor,
    this.backgroundColor = Colors.grey,
    this.showDot = false,
    this.bigDot = false,
  });

  @override
  State<ProgressIcon> createState() => _ProgressIconState();
}

class _ProgressIconState extends State<ProgressIcon> {
  late double _previousProgress;
  Color _effectColor = Colors.white;

  @override
  void initState() {
    super.initState();
    _previousProgress = widget.progress;
  }

  @override
  void didUpdateWidget(ProgressIcon oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.progress != _previousProgress) {
      // Xác định màu dựa trên sự thay đổi của progress
      _effectColor = widget.progress > _previousProgress
          ? Colors.green // <PERSON><PERSON><PERSON> xanh khi tăng
          : Colors.red;  // Màu đỏ khi giảm

      // Reset màu về trắng sau 1 giây
      Future.delayed(const Duration(seconds: 1), () {
        if (mounted) {
          setState(() {
            _effectColor = Colors.white;
          });
        }
      });

      _previousProgress = widget.progress;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topCenter,
      clipBehavior: Clip.none,
      children: [
        // ProgressIcon với animation màu
        TweenAnimationBuilder<double>(
          duration: const Duration(milliseconds: 500),
          tween: Tween<double>(begin: 0, end: widget.progress),
          builder: (context, animatedProgress, child) {
            return ShaderMask(
              shaderCallback: (bounds) {
                return LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  stops: [animatedProgress, animatedProgress],
                  colors: [_effectColor, widget.backgroundColor],
                ).createShader(bounds);
              },
              blendMode: BlendMode.srcIn,
              child: Icon(
                widget.icon,
                size: 35,
                color: Colors.white,
              ),
            );
          },
        ),
        // Dot indicators
        if(widget.showDot && !widget.bigDot)
          const Positioned(
            top: -15,
            child: CircleAvatar(
              radius: 2.5,
              backgroundColor: Colors.white,
            ),
          ),
        if (widget.showDot && widget.bigDot)
          const Positioned(
            top: -15,
            child: CircleAvatar(
              radius: 4.5,
              backgroundColor: Colors.white,
            ),
          ),
      ],
    );
  }
}