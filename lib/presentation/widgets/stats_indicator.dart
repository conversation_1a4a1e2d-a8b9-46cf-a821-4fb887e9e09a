import 'package:flutter/material.dart';
import 'progress_icon.dart';

class StatsIndicator extends StatelessWidget {
  final int internalTrust;
  final int externalTrust;
  final int mentalStability;
  final int socialLinks;
  final Map<String, int>? previewEffect;

  const StatsIndicator({
    super.key,
    required this.internalTrust,
    required this.externalTrust,
    required this.mentalStability,
    required this.socialLinks,
    this.previewEffect,
  });

  @override
  Widget build(BuildContext context) {
    final previewEffects = previewEffect ?? {};

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ProgressIcon(
            progress: internalTrust / 10,
            icon: Icons.remove_red_eye,
            fillColor: Colors.white,
            showDot: previewEffects['internalTrust'] != null &&
                previewEffects['internalTrust'] != 0,
            bigDot: previewEffects['internalTrust'] != null &&
                previewEffects['internalTrust']! >= 3,
          ),
          ProgressIcon(
            progress: externalTrust / 10,
            icon: Icons.gps_fixed,
            fillColor: Colors.white,
            showDot: previewEffects['externalTrust'] != null &&
                previewEffects['externalTrust'] != 0,
            bigDot: previewEffects['externalTrust'] != null &&
                previewEffects['externalTrust']! >= 3,
          ),
          ProgressIcon(
            progress: mentalStability / 10,
            icon: Icons.heart_broken,
            fillColor: Colors.white,
            showDot: previewEffects['mentalStability'] != null &&
                previewEffects['mentalStability'] != 0,
            bigDot: previewEffects['mentalStability'] != null &&
                previewEffects['mentalStability']! >= 3,
          ),
          ProgressIcon(
            progress: socialLinks / 10,
            icon: Icons.groups_3,
            fillColor: Colors.white,
            showDot: previewEffects['socialLinks'] != null &&
                previewEffects['socialLinks'] != 0,
            bigDot: previewEffects['socialLinks'] != null &&
                previewEffects['socialLinks']! >= 3,
          ),
        ],
      ),
    );
  }
}
