import 'package:flutter_test/flutter_test.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:spy/logic/service/ads_service.dart';

void main() {
  group('Account Change Tests', () {
    setUpAll(() async {
      await Hive.initFlutter();
    });

    tearDownAll(() async {
      await Hive.close();
    });

    test('AdsService should reset state when account changes', () async {
      final adsService = AdsService();
      await adsService.init();
      
      // Simulate user had purchased remove ads
      await adsService.setAdsRemoved(true);
      expect(adsService.isAdsRemoved, true);
      
      // Simulate account change - reset state
      await adsService.resetAdsState();
      expect(adsService.isAdsRemoved, false);
      
      // Verify showInterstitialAd works normally again
      bool onCompleteCalled = false;
      bool onFailedCalled = false;
      
      await adsService.showInterstitialAd(
        onComplete: () {
          onCompleteCalled = true;
        },
        onFailed: () {
          onFailedCalled = true;
        },
      );
      
      // Since ads are not removed, it should try to show ad and fail (no ad loaded in test)
      expect(onFailedCalled, true);
      expect(onCompleteCalled, false);
    });

    test('AdsService should handle purchase after reset', () async {
      final adsService = AdsService();
      await adsService.init();
      
      // Start with ads removed
      await adsService.setAdsRemoved(true);
      expect(adsService.isAdsRemoved, true);
      
      // Reset (simulate account change)
      await adsService.resetAdsState();
      expect(adsService.isAdsRemoved, false);
      
      // Purchase again (simulate new account purchasing)
      await adsService.handleRemoveAdsPurchased();
      expect(adsService.isAdsRemoved, true);
      
      // Verify showInterstitialAd calls onComplete immediately
      bool onCompleteCalled = false;
      
      await adsService.showInterstitialAd(
        onComplete: () {
          onCompleteCalled = true;
        },
        onFailed: () {},
      );
      
      expect(onCompleteCalled, true);
    });
  });
}
