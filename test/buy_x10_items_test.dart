import 'package:flutter_test/flutter_test.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:spy/logic/service/effect_service.dart';
import 'package:spy/logic/service/score_service.dart';

void main() {
  group('Buy x10 Items Tests', () {
    late EffectService effectService;
    late ScoreService scoreService;

    setUpAll(() async {
      await Hive.initFlutter();
    });

    setUp(() async {
      scoreService = ScoreService();
      await scoreService.init();
      effectService = EffectService(scoreService);
      await effectService.init();
    });

    tearDownAll(() async {
      await Hive.close();
    });

    test('addEffects should add 10 items correctly', () async {
      const itemType = 'Eye';
      
      // Initially should have 0
      expect(effectService.getEffectQuantity(itemType), 0);
      
      // Add 10 items
      await effectService.addEffects(itemType, 10);
      
      // Should now have 10
      expect(effectService.getEffectQuantity(itemType), 10);
      
      // Add 10 more
      await effectService.addEffects(itemType, 10);
      
      // Should now have 20
      expect(effectService.getEffectQuantity(itemType), 20);
    });

    test('addEffects should work for all item types', () async {
      const itemTypes = ['Eye', 'Military badge', 'Heart', 'Newspaper'];
      
      for (final itemType in itemTypes) {
        // Initially should have 0
        expect(effectService.getEffectQuantity(itemType), 0);
        
        // Add 10 items
        await effectService.addEffects(itemType, 10);
        
        // Should now have 10
        expect(effectService.getEffectQuantity(itemType), 10);
      }
    });

    test('addEffects should persist across service restarts', () async {
      const itemType = 'Heart';
      
      // Add 10 items
      await effectService.addEffects(itemType, 10);
      expect(effectService.getEffectQuantity(itemType), 10);
      
      // Create new service instance (simulate app restart)
      final newEffectService = EffectService(scoreService);
      await newEffectService.init();
      
      // Should still have 10 items
      expect(newEffectService.getEffectQuantity(itemType), 10);
    });

    test('addEffects should work with different quantities', () async {
      const itemType = 'Newspaper';
      
      // Add 5 items
      await effectService.addEffects(itemType, 5);
      expect(effectService.getEffectQuantity(itemType), 5);
      
      // Add 3 more items
      await effectService.addEffects(itemType, 3);
      expect(effectService.getEffectQuantity(itemType), 8);
      
      // Add 10 more items (simulating x10 purchase)
      await effectService.addEffects(itemType, 10);
      expect(effectService.getEffectQuantity(itemType), 18);
    });
  });
}
